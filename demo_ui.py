#!/usr/bin/env python3
"""
Demo script for FTP AutoSync UI with sample data
"""

import sys
import os
from pathlib import Path
import time
import threading

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from ftpautosync.database import DatabaseManager
from ftpautosync.ui.main_window import MainWindow


def create_demo_data():
    """Create demo sites and activity logs"""
    print("Creating demo data...")

    # Create demo database
    db = DatabaseManager("demo.db")

    # Demo site configurations
    demo_sites = [
        {
            'name': 'My Website',
            'config': {
                'ftp': {
                    'host': 'ftp.mywebsite.com',
                    'port': 21,
                    'username': 'webuser',
                    'password': '••••••••',
                    'passive': True,
                    'timeout': 30
                },
                'local': {
                    'watch_path': './demo_sites/website',
                    'exclude_patterns': ['*.tmp', '*.log', '.git/*', '__pycache__/*']
                },
                'remote': {
                    'base_path': '/public_html',
                    'create_dirs': True
                },
                'sync': {
                    'mode': 'upload_only',
                    'initial_sync': False,
                    'retry_attempts': 3,
                    'retry_delay': 5,
                    'delete_remote': False
                },
                'logging': {
                    'level': 'INFO',
                    'file': 'website.log',
                    'max_file_size': 10,
                    'backup_count': 5
                }
            }
        },
        {
            'name': 'Blog Content',
            'config': {
                'ftp': {
                    'host': 'ftp.myblog.com',
                    'port': 21,
                    'username': 'blogger',
                    'password': '••••••••',
                    'passive': True,
                    'timeout': 30
                },
                'local': {
                    'watch_path': './demo_sites/blog',
                    'exclude_patterns': ['*.tmp', '*.bak', 'drafts/*']
                },
                'remote': {
                    'base_path': '/blog',
                    'create_dirs': True
                },
                'sync': {
                    'mode': 'upload_only',
                    'initial_sync': True,
                    'retry_attempts': 5,
                    'retry_delay': 3,
                    'delete_remote': True
                },
                'logging': {
                    'level': 'DEBUG',
                    'file': 'blog.log',
                    'max_file_size': 5,
                    'backup_count': 3
                }
            }
        },
        {
            'name': 'Client Project',
            'config': {
                'ftp': {
                    'host': 'client.example.com',
                    'port': 21,
                    'username': 'client_user',
                    'password': '••••••••',
                    'passive': True,
                    'timeout': 45
                },
                'local': {
                    'watch_path': './demo_sites/client',
                    'exclude_patterns': ['*.tmp', '*.log', 'node_modules/*', '.env']
                },
                'remote': {
                    'base_path': '/htdocs',
                    'create_dirs': True
                },
                'sync': {
                    'mode': 'upload_only',
                    'initial_sync': False,
                    'retry_attempts': 2,
                    'retry_delay': 10,
                    'delete_remote': False
                },
                'logging': {
                    'level': 'WARNING',
                    'file': 'client.log',
                    'max_file_size': 20,
                    'backup_count': 10
                }
            }
        }
    ]

    # Create demo sites
    site_ids = []
    for site_data in demo_sites:
        # Create watch directory
        watch_path = Path(site_data['config']['local']['watch_path'])
        watch_path.mkdir(parents=True, exist_ok=True)

        # Add site to database
        site_id = db.add_site(site_data['name'], site_data['config'])
        site_ids.append(site_id)

        # Set some sites as enabled/disabled
        if site_data['name'] == 'Client Project':
            db.update_site(site_id, enabled=False)

        # Update site status with demo data
        if site_data['name'] == 'My Website':
            db.update_site_status(site_id, 'running', files_synced=45, errors_count=2)
        elif site_data['name'] == 'Blog Content':
            db.update_site_status(site_id, 'stopped', files_synced=23, errors_count=0)
        else:
            db.update_site_status(site_id, 'error', files_synced=12, errors_count=5)

    # Add demo activity logs
    demo_activities = [
        (site_ids[0], 'INFO', 'Site "My Website" started', None),
        (site_ids[0], 'INFO', 'Uploaded: index.html', '/demo_sites/website/index.html'),
        (site_ids[0], 'INFO', 'Uploaded: style.css', '/demo_sites/website/css/style.css'),
        (site_ids[0], 'WARNING', 'Retry attempt 1 for upload', 'Connection timeout'),
        (site_ids[0], 'INFO', 'Uploaded: script.js', '/demo_sites/website/js/script.js'),
        (site_ids[1], 'INFO', 'Site "Blog Content" started', None),
        (site_ids[1], 'INFO', 'Uploaded: new-post.md', '/demo_sites/blog/posts/new-post.md'),
        (site_ids[1], 'INFO', 'Site "Blog Content" stopped', 'Manual stop'),
        (site_ids[2], 'ERROR', 'Failed to connect to FTP server', 'Connection refused'),
        (site_ids[2], 'ERROR', 'Site "Client Project" stopped due to errors', None),
        (site_ids[0], 'INFO', 'Uploaded: contact.html', '/demo_sites/website/contact.html'),
        (site_ids[0], 'INFO', 'Deleted: old-page.html', '/public_html/old-page.html'),
    ]

    for site_id, level, message, details in demo_activities:
        db.add_activity_log(site_id, level, message, details)
        time.sleep(0.1)  # Small delay to create different timestamps

    print(f"✓ Created {len(demo_sites)} demo sites")
    print(f"✓ Added {len(demo_activities)} demo activity logs")

    return site_ids


def cleanup_demo_data():
    """Clean up demo data"""
    print("Cleaning up demo data...")

    # Remove demo directories
    import shutil
    demo_dir = Path('./demo_sites')
    if demo_dir.exists():
        shutil.rmtree(demo_dir)

    # Remove demo database
    demo_db = Path('demo.db')
    if demo_db.exists():
        demo_db.unlink()

    print("✓ Demo data cleaned up")


class DemoMainWindow(MainWindow):
    """Demo version of main window that uses demo database"""

    def __init__(self):
        # Import tkinter here
        import tkinter as tk

        # Override site manager to use demo database
        self.root = tk.Tk()
        from ftpautosync.site_manager import SiteManager
        self.site_manager = SiteManager("demo.db")
        self.refresh_timer = None

        self.setup_window()
        self.create_widgets()
        self.setup_callbacks()
        self.refresh_data()

        # Start auto-refresh
        self.start_auto_refresh()

        # Add demo notice
        self.add_demo_notice()

    def add_demo_notice(self):
        """Add demo notice to the window"""
        import tkinter as tk
        from tkinter import ttk

        # Update window title
        self.root.title("FTP AutoSync - DEMO MODE")

        # Add demo banner at the top
        demo_frame = ttk.Frame(self.root)
        demo_frame.pack(fill=tk.X, side=tk.TOP, before=self.root.children[list(self.root.children.keys())[0]] if self.root.children else None)

        # Configure demo style
        style = ttk.Style()
        try:
            style.configure('Demo.TFrame', background='#fff3cd')
            style.configure('Demo.TLabel', background='#fff3cd', foreground='#856404')
        except:
            pass

        demo_label = ttk.Label(
            demo_frame,
            text="🎭 DEMO MODE - Sample data only, no real FTP connections",
            font=('Arial', 12, 'bold')
        )
        demo_label.pack(pady=5)


def main():
    """Main demo function"""
    print("FTP AutoSync - Demo Mode")
    print("=" * 30)

    try:
        # Create demo data
        create_demo_data()

        print("\nLaunching demo UI...")
        print("Note: This is a demonstration with sample data.")
        print("No real FTP connections will be made.")

        # Import tkinter here to avoid issues if not available
        import tkinter as tk

        # Create and run demo app
        app = DemoMainWindow()

        try:
            app.run()
        except KeyboardInterrupt:
            print("\nDemo interrupted by user.")

    except Exception as e:
        print(f"❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()
        return 1

    finally:
        # Cleanup
        cleanup_demo_data()

    return 0


if __name__ == '__main__':
    sys.exit(main())
