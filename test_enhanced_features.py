#!/usr/bin/env python3
"""
Test script for enhanced FTP AutoSync features
"""

import sys
import os
from pathlib import Path
import tempfile
import shutil

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from ftpautosync.site_manager import SiteManager
from ftpautosync.database import DatabaseManager


def test_config_import():
    """Test importing legacy config.yaml"""
    print("Testing config import...")
    
    try:
        # Create test database
        test_db = "test_import.db"
        site_manager = SiteManager(test_db)
        
        # Test importing the existing config.yaml
        if Path("config.yaml").exists():
            site_id = site_manager.import_legacy_config("config.yaml")
            
            # Verify the site was created
            site = site_manager.get_site(site_id)
            assert site is not None, "Site should be created"
            assert site['name'] == "Imported Site (config)", "Site name should match"
            assert site['config']['ftp']['host'] == "www.abla.lat", "FTP host should match"
            
            print("✅ Config import test passed")
            
            # Cleanup
            site_manager.delete_site(site_id)
        else:
            print("⚠️  config.yaml not found, skipping import test")
        
        # Cleanup
        Path(test_db).unlink(missing_ok=True)
        return True
        
    except Exception as e:
        print(f"❌ Config import test failed: {e}")
        return False


def test_bulk_operations():
    """Test bulk upload/download operations"""
    print("Testing bulk operations...")
    
    try:
        # Create test database and site
        test_db = "test_bulk.db"
        site_manager = SiteManager(test_db)
        
        # Create test site with mock data
        test_config = {
            'ftp': {
                'host': 'test.example.com',
                'port': 21,
                'username': 'testuser',
                'password': 'testpass',
                'passive': True,
                'timeout': 30
            },
            'local': {
                'watch_path': './test_bulk_watch',
                'exclude_patterns': ['*.tmp']
            },
            'remote': {
                'base_path': '/test',
                'create_dirs': True
            },
            'sync': {
                'mode': 'upload_only',
                'initial_sync': False,
                'retry_attempts': 1,
                'retry_delay': 1,
                'delete_remote': False
            },
            'logging': {
                'level': 'INFO',
                'file': 'test.log',
                'max_file_size': 10,
                'backup_count': 5
            }
        }
        
        # Create test watch directory and files
        watch_dir = Path('./test_bulk_watch')
        watch_dir.mkdir(exist_ok=True)
        
        test_file = watch_dir / "test.txt"
        test_file.write_text("Test content for bulk operations")
        
        # Create site
        site_id = site_manager.create_site("Test Bulk Site", test_config)
        
        # Test bulk upload (will fail due to fake FTP, but should handle gracefully)
        results = site_manager.bulk_upload_all_sites()
        assert isinstance(results, dict), "Should return results dictionary"
        
        # Test bulk download (will fail due to fake FTP, but should handle gracefully)
        results = site_manager.bulk_download_all_sites("./test_downloads")
        assert isinstance(results, dict), "Should return results dictionary"
        
        print("✅ Bulk operations test passed (graceful failure handling)")
        
        # Cleanup
        site_manager.delete_site(site_id)
        shutil.rmtree(watch_dir, ignore_errors=True)
        shutil.rmtree("./test_downloads", ignore_errors=True)
        Path(test_db).unlink(missing_ok=True)
        
        return True
        
    except Exception as e:
        print(f"❌ Bulk operations test failed: {e}")
        return False


def test_ui_import_functionality():
    """Test UI import functionality"""
    print("Testing UI import functionality...")
    
    try:
        # Test that UI components can be imported
        from ftpautosync.ui.main_window import MainWindow
        
        # Test that new methods exist
        assert hasattr(MainWindow, 'import_config'), "Should have import_config method"
        assert hasattr(MainWindow, 'bulk_upload_all'), "Should have bulk_upload_all method"
        assert hasattr(MainWindow, 'bulk_download_all'), "Should have bulk_download_all method"
        
        print("✅ UI import functionality test passed")
        return True
        
    except Exception as e:
        print(f"❌ UI import functionality test failed: {e}")
        return False


def test_cli_bulk_operations():
    """Test CLI bulk operations"""
    print("Testing CLI bulk operations...")
    
    try:
        # Test that bulk_operations.py can be imported
        import subprocess
        
        # Test help command
        result = subprocess.run([sys.executable, 'bulk_operations.py', '--help'], 
                              capture_output=True, text=True)
        assert result.returncode == 0, "Help command should succeed"
        assert "import" in result.stdout, "Should show import command"
        assert "upload" in result.stdout, "Should show upload command"
        assert "download" in result.stdout, "Should show download command"
        
        print("✅ CLI bulk operations test passed")
        return True
        
    except Exception as e:
        print(f"❌ CLI bulk operations test failed: {e}")
        return False


def test_database_enhancements():
    """Test database enhancements"""
    print("Testing database enhancements...")
    
    try:
        # Create test database
        test_db = "test_db_enhancements.db"
        db = DatabaseManager(test_db)
        
        # Test site creation and retrieval
        test_config = {'ftp': {'host': 'test.com', 'username': 'user', 'password': 'pass'}, 
                      'local': {'watch_path': '/test'}, 'sync': {'mode': 'upload_only'}, 
                      'logging': {'level': 'INFO'}}
        
        site_id = db.add_site("Test Site", test_config)
        
        # Test activity logging
        db.add_activity_log(site_id, 'INFO', 'Test message', 'Test details')
        
        # Test statistics
        stats = db.get_statistics()
        assert stats['total_sites'] == 1, "Should have 1 site"
        assert stats['recent_activity'] == 1, "Should have 1 recent activity"
        
        # Test activity logs retrieval
        logs = db.get_activity_logs(site_id)
        assert len(logs) == 1, "Should have 1 log entry"
        assert logs[0]['message'] == 'Test message', "Message should match"
        
        print("✅ Database enhancements test passed")
        
        # Cleanup
        Path(test_db).unlink(missing_ok=True)
        return True
        
    except Exception as e:
        print(f"❌ Database enhancements test failed: {e}")
        return False


def main():
    """Main test function"""
    print("FTP AutoSync Enhanced Features Test Suite")
    print("=" * 50)
    
    tests = [
        test_database_enhancements,
        test_config_import,
        test_bulk_operations,
        test_ui_import_functionality,
        test_cli_bulk_operations
    ]
    
    passed = 0
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
    
    print(f"\n📊 Results: {passed}/{len(tests)} tests passed")
    
    if passed == len(tests):
        print("🎉 All enhanced features working correctly!")
        print("\nNew features available:")
        print("  • Import config.yaml: python bulk_operations.py import")
        print("  • List sites: python bulk_operations.py list")
        print("  • Bulk upload: python bulk_operations.py upload")
        print("  • Bulk download: python bulk_operations.py download")
        print("  • UI with import button and bulk operations")
        print("  • Enhanced database with activity logging")
        return 0
    else:
        print("❌ Some enhanced features need attention.")
        return 1


if __name__ == '__main__':
    sys.exit(main())
