# Systemd service file for FTP AutoSync
# Copy this file to /etc/systemd/system/ and edit paths as needed

[Unit]
Description=FTP AutoSync - Automatic file synchronization via FTP
After=network.target

[Service]
Type=simple
User=your_username
Group=your_group
WorkingDirectory=/path/to/ftpautosync
ExecStart=/usr/bin/python3 /path/to/ftpautosync/main.py
Restart=always
RestartSec=10

# Environment variables (optional)
# Environment=PYTHONPATH=/path/to/ftpautosync

# Logging
StandardOutput=journal
StandardError=journal
SyslogIdentifier=ftpautosync

[Install]
WantedBy=multi-user.target
