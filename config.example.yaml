# FTP AutoSync Configuration Example
# Copy this file to config.yaml and edit with your settings

# FTP Server Settings
ftp:
  host: "ftp.example.com"          # Your FTP server hostname
  port: 21                         # FTP port (usually 21)
  username: "your_username"        # Your FTP username
  password: "your_password"        # Your FTP password
  passive: true                    # Use passive mode (recommended)
  timeout: 30                      # Connection timeout in seconds

# Local directory to monitor
local:
  # Path to watch for changes (relative to this config file)
  watch_path: "./watch"
  # Patterns to exclude from sync (glob patterns)
  exclude_patterns:
    - "*.tmp"                      # Temporary files
    - "*.log"                      # Log files
    - ".git/*"                     # Git repository
    - "__pycache__/*"              # Python cache
    - "*.pyc"                      # Python compiled files
    - ".DS_Store"                  # macOS system files
    - "Thumbs.db"                  # Windows system files
    - "node_modules/*"             # Node.js dependencies
    - ".env"                       # Environment files

# Remote FTP settings
remote:
  # Base path on FTP server (empty for root directory)
  base_path: ""
  # Whether to create directories if they don't exist
  create_dirs: true
  # File permissions for uploaded files (optional)
  file_permissions: null

# Sync behavior
sync:
  # Sync mode: "upload_only" (bidirectional not implemented yet)
  mode: "upload_only"
  # Whether to sync existing files on startup
  initial_sync: false
  # Retry attempts for failed uploads
  retry_attempts: 3
  # Delay between retries in seconds
  retry_delay: 5
  # Whether to delete remote files when local files are deleted
  delete_remote: false

# Logging configuration
logging:
  # Log level: DEBUG, INFO, WARNING, ERROR, CRITICAL
  level: "INFO"
  # Log file path (leave empty to log only to console)
  file: "ftpautosync.log"
  # Maximum log file size in MB
  max_file_size: 10
  # Number of backup log files to keep
  backup_count: 5
