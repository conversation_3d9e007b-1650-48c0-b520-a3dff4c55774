# FTP AutoSync Makefile

.PHONY: help setup install ui run-cli test-config test-connection clean import-config bulk-upload bulk-download list-sites venv-ui venv-import venv-list venv-upload venv-download

help:
	@echo "FTP AutoSync - Available commands:"
	@echo ""
	@echo "Setup & Installation:"
	@echo "  setup          - Complete setup with virtual environment"
	@echo "  install        - Install dependencies (no venv)"
	@echo ""
	@echo "Virtual Environment (Recommended):"
	@echo "  venv-ui        - Launch UI in virtual environment"
	@echo "  venv-import    - Import config.yaml in virtual environment"
	@echo "  venv-list      - List sites in virtual environment"
	@echo "  venv-upload    - Bulk upload in virtual environment"
	@echo "  venv-download  - Bulk download in virtual environment"
	@echo ""
	@echo "Direct Commands (requires manual venv activation):"
	@echo "  ui             - Launch macOS UI application"
	@echo "  import-config  - Import config.yaml into multi-site database"
	@echo "  list-sites     - List all configured sites"
	@echo "  bulk-upload    - Upload all files from all enabled sites"
	@echo "  bulk-download  - Download all files from all enabled sites"
	@echo ""
	@echo "Command Line (Legacy):"
	@echo "  run-cli        - Start single-site CLI version"
	@echo "  test-config    - Test configuration file"
	@echo "  test-connection - Test FTP connection"
	@echo ""
	@echo "Maintenance:"
	@echo "  clean          - Clean up log files and cache"

setup:
	@echo "Running complete setup with virtual environment..."
	./setup.sh

install:
	@echo "Installing FTP AutoSync dependencies..."
	pip install -r requirements.txt
	@echo "✓ Installation completed"

# Virtual Environment Commands (Recommended)
venv-ui:
	@echo "Launching FTP AutoSync UI in virtual environment..."
	@if [ ! -d ".venv" ]; then echo "❌ Virtual environment not found. Run 'make setup' first."; exit 1; fi
	source .venv/bin/activate && python main_ui.py

venv-import:
	@echo "Importing config.yaml in virtual environment..."
	@if [ ! -d ".venv" ]; then echo "❌ Virtual environment not found. Run 'make setup' first."; exit 1; fi
	source .venv/bin/activate && python bulk_operations.py import

venv-list:
	@echo "Listing sites in virtual environment..."
	@if [ ! -d ".venv" ]; then echo "❌ Virtual environment not found. Run 'make setup' first."; exit 1; fi
	source .venv/bin/activate && python bulk_operations.py list

venv-upload:
	@echo "Starting bulk upload in virtual environment..."
	@if [ ! -d ".venv" ]; then echo "❌ Virtual environment not found. Run 'make setup' first."; exit 1; fi
	source .venv/bin/activate && python bulk_operations.py upload

venv-download:
	@echo "Starting bulk download in virtual environment..."
	@if [ ! -d ".venv" ]; then echo "❌ Virtual environment not found. Run 'make setup' first."; exit 1; fi
	source .venv/bin/activate && python bulk_operations.py download

# Direct Commands (requires manual venv activation)
ui:
	@echo "Launching FTP AutoSync UI..."
	python main_ui.py

import-config:
	@echo "Importing config.yaml into multi-site database..."
	python bulk_operations.py import

list-sites:
	@echo "Listing all configured sites..."
	python bulk_operations.py list

bulk-upload:
	@echo "Starting bulk upload for all enabled sites..."
	python bulk_operations.py upload

bulk-download:
	@echo "Starting bulk download for all enabled sites..."
	python bulk_operations.py download

test-config:
	@echo "Testing configuration..."
	python -c "from ftpautosync.config import ConfigManager; ConfigManager(); print('✓ Configuration is valid')"

test-connection:
	@echo "Testing FTP connection..."
	python main.py --test-connection

run-cli:
	@echo "Starting FTP AutoSync (CLI)..."
	python main.py

clean:
	@echo "Cleaning up..."
	rm -f *.log *.db
	rm -rf __pycache__
	rm -rf ftpautosync/__pycache__
	rm -rf ftpautosync/ui/__pycache__
	@echo "✓ Cleanup completed"

clean-all:
	@echo "Cleaning up everything including virtual environment..."
	rm -f *.log *.db
	rm -rf __pycache__
	rm -rf ftpautosync/__pycache__
	rm -rf ftpautosync/ui/__pycache__
	rm -rf .venv
	@echo "✓ Complete cleanup completed"
