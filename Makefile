# FTP AutoSync Makefile

.PHONY: help install ui run-cli test-config test-connection clean import-config bulk-upload bulk-download list-sites

help:
	@echo "FTP AutoSync - Available commands:"
	@echo ""
	@echo "Setup & Installation:"
	@echo "  install        - Install dependencies"
	@echo ""
	@echo "UI Application:"
	@echo "  ui             - Launch macOS UI application"
	@echo ""
	@echo "Bulk Operations:"
	@echo "  import-config  - Import config.yaml into multi-site database"
	@echo "  list-sites     - List all configured sites"
	@echo "  bulk-upload    - Upload all files from all enabled sites"
	@echo "  bulk-download  - Download all files from all enabled sites"
	@echo ""
	@echo "Command Line (Legacy):"
	@echo "  run-cli        - Start single-site CLI version"
	@echo "  test-config    - Test configuration file"
	@echo "  test-connection - Test FTP connection"
	@echo ""
	@echo "Maintenance:"
	@echo "  clean          - Clean up log files and cache"

install:
	@echo "Installing FTP AutoSync dependencies..."
	pip install -r requirements.txt
	@echo "✓ Installation completed"

ui:
	@echo "Launching FTP AutoSync UI..."
	python main_ui.py

import-config:
	@echo "Importing config.yaml into multi-site database..."
	python bulk_operations.py import

list-sites:
	@echo "Listing all configured sites..."
	python bulk_operations.py list

bulk-upload:
	@echo "Starting bulk upload for all enabled sites..."
	python bulk_operations.py upload

bulk-download:
	@echo "Starting bulk download for all enabled sites..."
	python bulk_operations.py download

test-config:
	@echo "Testing configuration..."
	python -c "from ftpautosync.config import ConfigManager; ConfigManager(); print('✓ Configuration is valid')"

test-connection:
	@echo "Testing FTP connection..."
	python main.py --test-connection

run-cli:
	@echo "Starting FTP AutoSync (CLI)..."
	python main.py

clean:
	@echo "Cleaning up..."
	rm -f *.log *.db
	rm -rf __pycache__
	rm -rf ftpautosync/__pycache__
	rm -rf ftpautosync/ui/__pycache__
	@echo "✓ Cleanup completed"
