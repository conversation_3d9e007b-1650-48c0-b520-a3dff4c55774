#!/usr/bin/env python3
"""
Simple launcher for FTP AutoSync UI
"""

import sys
import os
import subprocess
from pathlib import Path


def check_dependencies():
    """Check if all dependencies are installed"""
    try:
        import watchdog
        import yaml
        import colorlog
        import tkinter
        print("✓ All dependencies are installed")
        return True
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        print("Please run: pip install -r requirements.txt")
        return False


def check_python_version():
    """Check Python version"""
    if sys.version_info < (3, 8):
        print(f"❌ Python 3.8+ required, you have {sys.version}")
        return False
    print(f"✓ Python version: {sys.version}")
    return True


def main():
    """Main launcher function"""
    print("FTP AutoSync Launcher")
    print("=" * 30)
    
    # Check Python version
    if not check_python_version():
        return 1
    
    # Check dependencies
    if not check_dependencies():
        return 1
    
    # Check if main_ui.py exists
    ui_script = Path("main_ui.py")
    if not ui_script.exists():
        print("❌ main_ui.py not found")
        return 1
    
    print("✓ All checks passed")
    print("\nLaunching FTP AutoSync UI...")
    
    try:
        # Launch the UI
        subprocess.run([sys.executable, str(ui_script)], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to launch UI: {e}")
        return 1
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
        return 0
    
    return 0


if __name__ == '__main__':
    sys.exit(main())
