"""
Multi-site management for FTP AutoSync
"""

import threading
import time
from datetime import datetime
from typing import Dict, List, Optional, Callable
from pathlib import Path

from .database import DatabaseManager
from .sync_engine import SyncEngine
from .config import ConfigManager
from .logger import Logger


class SiteManager:
    """Manages multiple FTP sync sites"""
    
    def __init__(self, db_path: str = "ftpautosync.db"):
        self.db = DatabaseManager(db_path)
        self.running_sites: Dict[str, SyncEngine] = {}
        self.site_threads: Dict[str, threading.Thread] = {}
        self.status_callbacks: List[Callable] = []
        self.log_callbacks: List[Callable] = []
        
        # Global logger for site manager
        self.logger = None
        self._setup_logger()
    
    def _setup_logger(self):
        """Setup logger for site manager"""
        # Create a minimal config for the site manager logger
        config = {
            'logging': {
                'level': 'INFO',
                'file': 'site_manager.log',
                'max_file_size': 10,
                'backup_count': 5
            }
        }
        logger_manager = Logger(config)
        self.logger = logger_manager.get_logger()
    
    def add_status_callback(self, callback: Callable):
        """Add callback for status updates"""
        self.status_callbacks.append(callback)
    
    def add_log_callback(self, callback: Callable):
        """Add callback for log updates"""
        self.log_callbacks.append(callback)
    
    def _notify_status_change(self, site_id: str, status: str):
        """Notify all status callbacks"""
        for callback in self.status_callbacks:
            try:
                callback(site_id, status)
            except Exception as e:
                self.logger.error(f"Error in status callback: {e}")
    
    def _notify_log_entry(self, site_id: str, level: str, message: str, details: str = None):
        """Notify all log callbacks and store in database"""
        # Store in database
        self.db.add_activity_log(site_id, level, message, details)
        
        # Notify callbacks
        for callback in self.log_callbacks:
            try:
                callback(site_id, level, message, details)
            except Exception as e:
                self.logger.error(f"Error in log callback: {e}")
    
    def create_site(self, name: str, config: Dict) -> str:
        """Create a new site"""
        try:
            # Validate configuration
            self._validate_site_config(config)
            
            # Add to database
            site_id = self.db.add_site(name, config)
            
            self.logger.info(f"Created new site: {name} (ID: {site_id})")
            self._notify_log_entry(site_id, 'INFO', f'Site "{name}" created')
            
            return site_id
            
        except Exception as e:
            self.logger.error(f"Failed to create site {name}: {e}")
            raise
    
    def update_site(self, site_id: str, name: str = None, config: Dict = None, enabled: bool = None):
        """Update site configuration"""
        try:
            if config:
                self._validate_site_config(config)
            
            # Stop site if it's running and config is being changed
            if config and self.is_site_running(site_id):
                self.stop_site(site_id)
            
            self.db.update_site(site_id, name, config, enabled)
            
            site = self.db.get_site(site_id)
            if site:
                self.logger.info(f"Updated site: {site['name']} (ID: {site_id})")
                self._notify_log_entry(site_id, 'INFO', f'Site "{site["name"]}" updated')
            
        except Exception as e:
            self.logger.error(f"Failed to update site {site_id}: {e}")
            raise
    
    def delete_site(self, site_id: str):
        """Delete a site"""
        try:
            site = self.db.get_site(site_id)
            if not site:
                raise ValueError(f"Site {site_id} not found")
            
            # Stop site if running
            if self.is_site_running(site_id):
                self.stop_site(site_id)
            
            self.db.delete_site(site_id)
            
            self.logger.info(f"Deleted site: {site['name']} (ID: {site_id})")
            
        except Exception as e:
            self.logger.error(f"Failed to delete site {site_id}: {e}")
            raise
    
    def get_site(self, site_id: str) -> Optional[Dict]:
        """Get site configuration"""
        return self.db.get_site(site_id)
    
    def get_all_sites(self) -> List[Dict]:
        """Get all sites"""
        return self.db.get_all_sites()
    
    def start_site(self, site_id: str):
        """Start monitoring a site"""
        try:
            if self.is_site_running(site_id):
                raise ValueError(f"Site {site_id} is already running")
            
            site = self.db.get_site(site_id)
            if not site:
                raise ValueError(f"Site {site_id} not found")
            
            if not site['enabled']:
                raise ValueError(f"Site {site['name']} is disabled")
            
            # Create temporary config file for this site
            config_data = site['config']
            temp_config = SiteConfigManager(site_id, config_data)
            
            # Create sync engine
            sync_engine = SyncEngine(temp_config, self._create_site_logger(site_id, site['name']))
            
            # Add custom callbacks to sync engine for database updates
            self._setup_sync_engine_callbacks(sync_engine, site_id)
            
            # Start in separate thread
            def run_site():
                try:
                    self.logger.info(f"Starting site: {site['name']} (ID: {site_id})")
                    self._notify_log_entry(site_id, 'INFO', f'Starting monitoring for "{site["name"]}"')
                    
                    sync_engine.start()
                    self.db.update_site_status(site_id, 'running')
                    self._notify_status_change(site_id, 'running')
                    
                    # Keep thread alive while sync engine is running
                    while sync_engine.running:
                        time.sleep(1)
                        
                except Exception as e:
                    self.logger.error(f"Error running site {site_id}: {e}")
                    self._notify_log_entry(site_id, 'ERROR', f'Site error: {str(e)}')
                    self.db.update_site_status(site_id, 'error')
                    self._notify_status_change(site_id, 'error')
                finally:
                    # Cleanup
                    if site_id in self.running_sites:
                        del self.running_sites[site_id]
                    if site_id in self.site_threads:
                        del self.site_threads[site_id]
            
            thread = threading.Thread(target=run_site, daemon=True)
            thread.start()
            
            self.running_sites[site_id] = sync_engine
            self.site_threads[site_id] = thread
            
        except Exception as e:
            self.logger.error(f"Failed to start site {site_id}: {e}")
            self._notify_log_entry(site_id, 'ERROR', f'Failed to start: {str(e)}')
            raise
    
    def stop_site(self, site_id: str):
        """Stop monitoring a site"""
        try:
            if not self.is_site_running(site_id):
                return
            
            site = self.db.get_site(site_id)
            sync_engine = self.running_sites.get(site_id)
            
            if sync_engine:
                self.logger.info(f"Stopping site: {site['name'] if site else site_id} (ID: {site_id})")
                self._notify_log_entry(site_id, 'INFO', f'Stopping monitoring for "{site["name"] if site else "Unknown"}"')
                
                sync_engine.stop()
                
                # Wait for thread to finish
                thread = self.site_threads.get(site_id)
                if thread and thread.is_alive():
                    thread.join(timeout=10)
                
                self.db.update_site_status(site_id, 'stopped')
                self._notify_status_change(site_id, 'stopped')
                
                # Cleanup
                if site_id in self.running_sites:
                    del self.running_sites[site_id]
                if site_id in self.site_threads:
                    del self.site_threads[site_id]
            
        except Exception as e:
            self.logger.error(f"Failed to stop site {site_id}: {e}")
            self._notify_log_entry(site_id, 'ERROR', f'Failed to stop: {str(e)}')
            raise
    
    def is_site_running(self, site_id: str) -> bool:
        """Check if site is currently running"""
        return site_id in self.running_sites and self.running_sites[site_id].running
    
    def get_site_status(self, site_id: str) -> Dict:
        """Get detailed site status"""
        db_status = self.db.get_site_status(site_id)
        is_running = self.is_site_running(site_id)
        
        status = {
            'running': is_running,
            'status': 'running' if is_running else (db_status['status'] if db_status else 'stopped'),
            'last_sync': db_status['last_sync'] if db_status else None,
            'files_synced': db_status['files_synced'] if db_status else 0,
            'errors_count': db_status['errors_count'] if db_status else 0,
            'updated_at': db_status['updated_at'] if db_status else None
        }
        
        # Add real-time info if running
        if is_running:
            sync_engine = self.running_sites[site_id]
            engine_status = sync_engine.get_status()
            status.update({
                'ftp_connected': engine_status['ftp_connected'],
                'upload_queue_size': engine_status['upload_queue_size'],
                'delete_queue_size': engine_status['delete_queue_size'],
                'watch_path': engine_status['watch_path']
            })
        
        return status
    
    def start_all_enabled_sites(self):
        """Start all enabled sites"""
        sites = self.db.get_all_sites()
        for site in sites:
            if site['enabled'] and not self.is_site_running(site['id']):
                try:
                    self.start_site(site['id'])
                except Exception as e:
                    self.logger.error(f"Failed to start site {site['name']}: {e}")
    
    def stop_all_sites(self):
        """Stop all running sites"""
        running_site_ids = list(self.running_sites.keys())
        for site_id in running_site_ids:
            try:
                self.stop_site(site_id)
            except Exception as e:
                self.logger.error(f"Failed to stop site {site_id}: {e}")
    
    def get_activity_logs(self, site_id: str = None, limit: int = 100) -> List[Dict]:
        """Get activity logs"""
        return self.db.get_activity_logs(site_id, limit)
    
    def get_statistics(self) -> Dict:
        """Get overall statistics"""
        return self.db.get_statistics()

    def import_legacy_config(self, config_path: str = "config.yaml") -> str:
        """Import legacy config.yaml file as a new site"""
        try:
            from .config import ConfigManager
            from pathlib import Path

            config_file = Path(config_path)
            if not config_file.exists():
                raise FileNotFoundError(f"Configuration file not found: {config_path}")

            # Load the legacy config
            legacy_config = ConfigManager(config_path)

            # Extract site name from config or use default
            base_name = f"Imported Site ({config_file.stem})"
            site_name = base_name

            # Check for duplicate names and add number if needed
            existing_sites = self.get_all_sites()
            existing_names = [site['name'] for site in existing_sites]
            counter = 1
            while site_name in existing_names:
                site_name = f"{base_name} ({counter})"
                counter += 1

            # Convert to new format
            new_config = {
                'ftp': legacy_config.get_ftp_config(),
                'local': legacy_config.get_local_config(),
                'remote': legacy_config.get_remote_config(),
                'sync': legacy_config.get_sync_config(),
                'logging': legacy_config.get_logging_config()
            }

            # Create the site
            site_id = self.create_site(site_name, new_config)

            self.logger.info(f"Imported legacy config from {config_path} as site: {site_name}")
            self._notify_log_entry(site_id, 'INFO', f'Imported from {config_path}')

            return site_id

        except Exception as e:
            self.logger.error(f"Failed to import legacy config: {e}")
            raise

    def bulk_upload_all_sites(self, force_sync: bool = False) -> Dict[str, bool]:
        """Upload all files from all enabled sites"""
        results = {}
        sites = self.get_all_sites()

        for site in sites:
            if not site['enabled']:
                continue

            site_id = site['id']
            site_name = site['name']

            try:
                self.logger.info(f"Starting bulk upload for site: {site_name}")
                self._notify_log_entry(site_id, 'INFO', f'Starting bulk upload for "{site_name}"')

                # Create temporary sync engine for this operation
                temp_config = SiteConfigManager(site_id, site['config'])
                temp_logger = self._create_site_logger(site_id, site_name)
                sync_engine = SyncEngine(temp_config, temp_logger)

                # Setup callbacks
                self._setup_sync_engine_callbacks(sync_engine, site_id)

                # Connect to FTP
                if not sync_engine.ftp_client.connect():
                    raise Exception("Cannot connect to FTP server")

                # Get all files in watch directory
                watch_path = temp_config.get_watch_path()
                uploaded_count = 0
                total_files = 0
                excluded_files = 0
                failed_files = 0

                self.logger.info(f"Scanning directory: {watch_path}")

                for file_path in watch_path.rglob('*'):
                    if file_path.is_file():
                        total_files += 1

                        # Check exclusion patterns
                        from .monitor import FileChangeHandler
                        handler = FileChangeHandler(sync_engine, temp_config, temp_logger)

                        if handler.should_exclude(file_path):
                            excluded_files += 1
                            self.logger.debug(f"Excluded: {file_path.name}")
                            continue

                        remote_path = sync_engine.get_remote_path(file_path)
                        if remote_path:
                            self.logger.info(f"Uploading: {file_path.name} -> {remote_path}")
                            if sync_engine.ftp_client.upload_file(file_path, remote_path):
                                uploaded_count += 1
                                self.logger.info(f"✅ Uploaded: {file_path.name}")
                            else:
                                failed_files += 1
                                self.logger.error(f"❌ Failed to upload: {file_path.name}")
                        else:
                            failed_files += 1
                            self.logger.error(f"❌ Could not determine remote path for: {file_path.name}")

                self.logger.info(f"Upload summary - Total: {total_files}, Uploaded: {uploaded_count}, Excluded: {excluded_files}, Failed: {failed_files}")

                sync_engine.ftp_client.disconnect()

                results[site_name] = True
                self.logger.info(f"Bulk upload completed for {site_name}: {uploaded_count} files")
                self._notify_log_entry(site_id, 'INFO', f'Bulk upload completed: {uploaded_count} files')

            except Exception as e:
                results[site_name] = False
                self.logger.error(f"Bulk upload failed for {site_name}: {e}")
                self._notify_log_entry(site_id, 'ERROR', f'Bulk upload failed: {str(e)}')

        return results

    def bulk_download_all_sites(self, target_dir: str = None) -> Dict[str, bool]:
        """Download all files from all enabled sites"""
        results = {}
        sites = self.get_all_sites()

        for site in sites:
            if not site['enabled']:
                continue

            site_id = site['id']
            site_name = site['name']

            try:
                self.logger.info(f"Starting bulk download for site: {site_name}")
                self._notify_log_entry(site_id, 'INFO', f'Starting bulk download for "{site_name}"')

                # Create temporary sync engine for this operation
                temp_config = SiteConfigManager(site_id, site['config'])
                temp_logger = self._create_site_logger(site_id, site_name)
                sync_engine = SyncEngine(temp_config, temp_logger)

                # Connect to FTP
                if not sync_engine.ftp_client.connect():
                    raise Exception("Cannot connect to FTP server")

                # Determine download directory
                if target_dir:
                    download_dir = Path(target_dir) / f"download_{site_name.replace(' ', '_')}"
                else:
                    download_dir = temp_config.get_watch_path() / "downloaded"

                download_dir.mkdir(parents=True, exist_ok=True)

                # Get remote file list and download
                downloaded_count = self._download_remote_directory(
                    sync_engine.ftp_client,
                    temp_config.get_remote_config().get('base_path', ''),
                    download_dir,
                    site_id
                )

                sync_engine.ftp_client.disconnect()

                results[site_name] = True
                self.logger.info(f"Bulk download completed for {site_name}: {downloaded_count} files")
                self._notify_log_entry(site_id, 'INFO', f'Bulk download completed: {downloaded_count} files to {download_dir}')

            except Exception as e:
                results[site_name] = False
                self.logger.error(f"Bulk download failed for {site_name}: {e}")
                self._notify_log_entry(site_id, 'ERROR', f'Bulk download failed: {str(e)}')

        return results
    
    def _validate_site_config(self, config: Dict):
        """Validate site configuration"""
        required_sections = ['ftp', 'local', 'sync', 'logging']
        for section in required_sections:
            if section not in config:
                raise ValueError(f"Missing required configuration section: {section}")
        
        # Validate FTP config
        ftp_config = config['ftp']
        required_ftp_fields = ['host', 'username', 'password']
        for field in required_ftp_fields:
            if field not in ftp_config:
                raise ValueError(f"Missing required FTP field: {field}")
        
        # Validate local config
        local_config = config['local']
        if 'watch_path' not in local_config:
            raise ValueError("Missing required field: local.watch_path")
        
        # Validate watch path exists or can be created
        watch_path = Path(local_config['watch_path'])
        try:
            watch_path.mkdir(parents=True, exist_ok=True)
        except Exception as e:
            raise ValueError(f"Cannot create watch path {watch_path}: {e}")
    
    def _create_site_logger(self, site_id: str, site_name: str):
        """Create a logger for a specific site"""
        config = {
            'logging': {
                'level': 'INFO',
                'file': f'site_{site_id}.log',
                'max_file_size': 10,
                'backup_count': 5
            }
        }
        logger_manager = Logger(config)
        return logger_manager.get_logger()
    
    def _setup_sync_engine_callbacks(self, sync_engine: SyncEngine, site_id: str):
        """Setup callbacks for sync engine to update database"""
        original_upload = sync_engine.ftp_client.upload_file
        original_delete = sync_engine.ftp_client.delete_file
        
        def upload_with_logging(local_path, remote_path):
            result = original_upload(local_path, remote_path)
            if result:
                # Update files synced count
                status = self.db.get_site_status(site_id)
                files_synced = (status['files_synced'] if status else 0) + 1
                self.db.update_site_status(site_id, 'running', datetime.now(), files_synced)
                self._notify_log_entry(site_id, 'INFO', f'Uploaded: {local_path.name}', str(local_path))
            else:
                # Update error count
                status = self.db.get_site_status(site_id)
                errors_count = (status['errors_count'] if status else 0) + 1
                self.db.update_site_status(site_id, 'running', errors_count=errors_count)
                self._notify_log_entry(site_id, 'ERROR', f'Failed to upload: {local_path.name}', str(local_path))
            return result
        
        def delete_with_logging(remote_path):
            result = original_delete(remote_path)
            if result:
                self._notify_log_entry(site_id, 'INFO', f'Deleted: {Path(remote_path).name}', remote_path)
            else:
                status = self.db.get_site_status(site_id)
                errors_count = (status['errors_count'] if status else 0) + 1
                self.db.update_site_status(site_id, 'running', errors_count=errors_count)
                self._notify_log_entry(site_id, 'ERROR', f'Failed to delete: {Path(remote_path).name}', remote_path)
            return result
        
        # Replace methods with logging versions
        sync_engine.ftp_client.upload_file = upload_with_logging
        sync_engine.ftp_client.delete_file = delete_with_logging

    def _download_remote_directory(self, ftp_client, remote_path: str, local_dir: Path, site_id: str) -> int:
        """Recursively download files from remote directory"""
        downloaded_count = 0

        try:
            # Change to remote directory
            if remote_path:
                ftp_client.ftp.cwd(remote_path)

            # Get file list
            file_list = []
            try:
                ftp_client.ftp.retrlines('LIST', file_list.append)
            except Exception as e:
                self.logger.warning(f"Could not list remote directory {remote_path}: {e}")
                return 0

            for line in file_list:
                # Parse FTP LIST output (basic parsing)
                parts = line.split()
                if len(parts) < 9:
                    continue

                permissions = parts[0]
                filename = ' '.join(parts[8:])

                # Skip . and .. entries
                if filename in ['.', '..']:
                    continue

                local_file_path = local_dir / filename

                if permissions.startswith('d'):
                    # Directory - recurse
                    local_file_path.mkdir(exist_ok=True)
                    sub_remote_path = f"{remote_path}/{filename}" if remote_path else filename
                    downloaded_count += self._download_remote_directory(
                        ftp_client, sub_remote_path, local_file_path, site_id
                    )
                else:
                    # File - download
                    try:
                        remote_file_path = f"{remote_path}/{filename}" if remote_path else filename
                        with open(local_file_path, 'wb') as local_file:
                            ftp_client.ftp.retrbinary(f'RETR {remote_file_path}', local_file.write)
                        downloaded_count += 1
                        self._notify_log_entry(site_id, 'INFO', f'Downloaded: {filename}', str(local_file_path))
                    except Exception as e:
                        self.logger.warning(f"Failed to download {filename}: {e}")
                        self._notify_log_entry(site_id, 'WARNING', f'Failed to download: {filename}', str(e))

            # Return to original directory
            if remote_path:
                ftp_client.ftp.cwd('/')

        except Exception as e:
            self.logger.error(f"Error downloading from {remote_path}: {e}")

        return downloaded_count


class SiteConfigManager:
    """Temporary config manager for individual sites"""
    
    def __init__(self, site_id: str, config_data: Dict):
        self.site_id = site_id
        self.config = config_data
    
    def get(self, key_path: str, default=None):
        """Get configuration value using dot notation"""
        keys = key_path.split('.')
        value = self.config
        
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default
    
    def get_ftp_config(self) -> Dict:
        return self.config['ftp']
    
    def get_local_config(self) -> Dict:
        return self.config['local']
    
    def get_remote_config(self) -> Dict:
        return self.config.get('remote', {})
    
    def get_sync_config(self) -> Dict:
        return self.config['sync']
    
    def get_logging_config(self) -> Dict:
        return self.config['logging']
    
    def get_watch_path(self) -> Path:
        watch_path = self.config['local']['watch_path']
        return Path(watch_path).expanduser().resolve()
    
    def get_exclude_patterns(self) -> list:
        return self.config['local'].get('exclude_patterns', [])
