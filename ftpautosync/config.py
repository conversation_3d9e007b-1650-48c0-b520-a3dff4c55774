"""
Configuration management for FTP AutoSync
"""

import yaml
import os
from pathlib import Path
from typing import Dict, Any


class ConfigManager:
    """Manages configuration loading and validation"""
    
    def __init__(self, config_path: str = "config.yaml"):
        self.config_path = Path(config_path)
        self.config = {}
        self.load_config()
        self.validate_config()
    
    def load_config(self):
        """Load configuration from YAML file"""
        if not self.config_path.exists():
            raise FileNotFoundError(f"Configuration file not found: {self.config_path}")
        
        try:
            with open(self.config_path, 'r', encoding='utf-8') as file:
                self.config = yaml.safe_load(file)
        except yaml.YAMLError as e:
            raise ValueError(f"Invalid YAML in configuration file: {e}")
        except Exception as e:
            raise ValueError(f"Error reading configuration file: {e}")
    
    def validate_config(self):
        """Validate required configuration fields"""
        required_fields = {
            'ftp': ['host', 'username', 'password'],
            'local': ['watch_path'],
            'sync': ['mode'],
            'logging': ['level']
        }
        
        for section, fields in required_fields.items():
            if section not in self.config:
                raise ValueError(f"Missing required configuration section: {section}")
            
            for field in fields:
                if field not in self.config[section]:
                    raise ValueError(f"Missing required field: {section}.{field}")
        
        # Validate sync mode
        valid_modes = ['upload_only', 'bidirectional']
        if self.config['sync']['mode'] not in valid_modes:
            raise ValueError(f"Invalid sync mode. Must be one of: {valid_modes}")
        
        # Validate log level
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if self.config['logging']['level'] not in valid_levels:
            raise ValueError(f"Invalid log level. Must be one of: {valid_levels}")
    
    def get(self, key_path: str, default=None):
        """Get configuration value using dot notation (e.g., 'ftp.host')"""
        keys = key_path.split('.')
        value = self.config
        
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default
    
    def get_ftp_config(self) -> Dict[str, Any]:
        """Get FTP configuration"""
        return self.config['ftp']
    
    def get_local_config(self) -> Dict[str, Any]:
        """Get local configuration"""
        return self.config['local']
    
    def get_remote_config(self) -> Dict[str, Any]:
        """Get remote configuration"""
        return self.config.get('remote', {})
    
    def get_sync_config(self) -> Dict[str, Any]:
        """Get sync configuration"""
        return self.config['sync']
    
    def get_logging_config(self) -> Dict[str, Any]:
        """Get logging configuration"""
        return self.config['logging']
    
    def get_watch_path(self) -> Path:
        """Get the watch path as a Path object"""
        watch_path = self.config['local']['watch_path']
        return Path(watch_path).expanduser().resolve()
    
    def get_exclude_patterns(self) -> list:
        """Get exclude patterns"""
        return self.config['local'].get('exclude_patterns', [])
