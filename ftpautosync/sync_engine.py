"""
Main synchronization engine
"""

import threading
import queue
import time
from pathlib import Path
from typing import Union
import logging

from .ftp_client import FTPClient
from .monitor import FileMonitor


class SyncEngine:
    """Main synchronization engine that coordinates file monitoring and FTP operations"""
    
    def __init__(self, config, logger):
        self.config = config
        self.logger = logger
        self.ftp_client = FTPClient(config, logger)
        self.file_monitor = FileMonitor(self, config, logger)
        
        # Queue for file operations
        self.upload_queue = queue.Queue()
        self.delete_queue = queue.Queue()
        
        # Worker thread for processing queues
        self.worker_thread = None
        self.running = False
        
        # Watch path configuration
        self.watch_path = config.get_watch_path()
        self.base_path = config.get_remote_config().get('base_path', '')
    
    def get_remote_path(self, local_path: Path) -> str:
        """Convert local path to remote path"""
        try:
            # Get relative path from watch directory
            relative_path = local_path.relative_to(self.watch_path)
            
            # Convert to forward slashes for FTP
            remote_path = str(relative_path).replace('\\', '/')
            
            # Combine with base path if specified
            if self.base_path:
                remote_path = f"{self.base_path.rstrip('/')}/{remote_path}"
            
            return remote_path
            
        except ValueError:
            # File is not under watch path
            self.logger.error(f"File {local_path} is not under watch path {self.watch_path}")
            return None
    
    def queue_upload(self, local_path: Path):
        """Queue a file for upload"""
        if local_path.exists() and local_path.is_file():
            self.upload_queue.put(local_path)
            self.logger.debug(f"Queued for upload: {local_path}")
        else:
            self.logger.warning(f"Cannot queue non-existent file: {local_path}")
    
    def queue_delete(self, local_path: Path):
        """Queue a file for deletion from remote server"""
        self.delete_queue.put(local_path)
        self.logger.debug(f"Queued for deletion: {local_path}")
    
    def process_upload_queue(self):
        """Process files in upload queue"""
        while not self.upload_queue.empty():
            try:
                local_path = self.upload_queue.get_nowait()
                
                # Check if file still exists (might have been deleted)
                if not local_path.exists():
                    self.logger.warning(f"File no longer exists, skipping upload: {local_path}")
                    continue
                
                remote_path = self.get_remote_path(local_path)
                if remote_path:
                    success = self.ftp_client.upload_file(local_path, remote_path)
                    if not success:
                        # Re-queue for retry later
                        self.upload_queue.put(local_path)
                        time.sleep(1)  # Brief delay before retry
                
            except queue.Empty:
                break
            except Exception as e:
                self.logger.error(f"Error processing upload queue: {e}")
    
    def process_delete_queue(self):
        """Process files in delete queue"""
        while not self.delete_queue.empty():
            try:
                local_path = self.delete_queue.get_nowait()
                remote_path = self.get_remote_path(local_path)
                
                if remote_path:
                    success = self.ftp_client.delete_file(remote_path)
                    if not success:
                        # Re-queue for retry later
                        self.delete_queue.put(local_path)
                        time.sleep(1)  # Brief delay before retry
                
            except queue.Empty:
                break
            except Exception as e:
                self.logger.error(f"Error processing delete queue: {e}")
    
    def worker_loop(self):
        """Main worker loop for processing queues"""
        self.logger.info("Sync worker thread started")
        
        while self.running:
            try:
                # Process queues
                self.process_upload_queue()
                self.process_delete_queue()
                
                # Brief sleep to prevent busy waiting
                time.sleep(0.5)
                
            except Exception as e:
                self.logger.error(f"Error in worker loop: {e}")
                time.sleep(5)  # Longer sleep on error
        
        self.logger.info("Sync worker thread stopped")
    
    def perform_initial_sync(self):
        """Perform initial sync of existing files if configured"""
        if not self.config.get_sync_config().get('initial_sync', False):
            return
        
        self.logger.info("Performing initial sync of existing files...")
        
        try:
            # Walk through watch directory and queue all files
            for file_path in self.watch_path.rglob('*'):
                if file_path.is_file():
                    # Check exclusion patterns
                    from .monitor import FileChangeHandler
                    handler = FileChangeHandler(self, self.config, self.logger)
                    
                    if not handler.should_exclude(file_path):
                        self.queue_upload(file_path)
            
            self.logger.info("Initial sync queued successfully")
            
        except Exception as e:
            self.logger.error(f"Error during initial sync: {e}")
    
    def start(self):
        """Start the sync engine"""
        if self.running:
            self.logger.warning("Sync engine is already running")
            return
        
        self.logger.info("Starting FTP AutoSync engine...")
        
        # Test FTP connection
        if not self.ftp_client.connect():
            raise Exception("Cannot connect to FTP server")
        
        self.running = True
        
        # Start worker thread
        self.worker_thread = threading.Thread(target=self.worker_loop, daemon=True)
        self.worker_thread.start()
        
        # Perform initial sync if configured
        self.perform_initial_sync()
        
        # Start file monitoring
        self.file_monitor.start()
        
        self.logger.info("FTP AutoSync engine started successfully")
    
    def stop(self):
        """Stop the sync engine"""
        if not self.running:
            return
        
        self.logger.info("Stopping FTP AutoSync engine...")
        
        # Stop file monitoring
        self.file_monitor.stop()
        
        # Stop worker thread
        self.running = False
        if self.worker_thread and self.worker_thread.is_alive():
            self.worker_thread.join(timeout=10)
        
        # Process remaining items in queues
        self.logger.info("Processing remaining items in queues...")
        self.process_upload_queue()
        self.process_delete_queue()
        
        # Disconnect FTP
        self.ftp_client.disconnect()
        
        self.logger.info("FTP AutoSync engine stopped")
    
    def get_status(self) -> dict:
        """Get current status of sync engine"""
        return {
            'running': self.running,
            'ftp_connected': self.ftp_client.connected,
            'monitor_running': self.file_monitor.is_running(),
            'upload_queue_size': self.upload_queue.qsize(),
            'delete_queue_size': self.delete_queue.qsize(),
            'watch_path': str(self.watch_path)
        }
