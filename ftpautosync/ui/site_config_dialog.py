"""
Site configuration dialog for FTP AutoSync
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from pathlib import Path
import copy


class SiteConfigDialog:
    """Dialog for creating/editing site configurations"""
    
    def __init__(self, parent, title, site_name="", config=None):
        self.parent = parent
        self.result = None
        
        # Default configuration
        self.default_config = {
            'ftp': {
                'host': '',
                'port': 21,
                'username': '',
                'password': '',
                'passive': True,
                'timeout': 30
            },
            'local': {
                'watch_path': '',
                'exclude_patterns': [
                    '*.tmp',
                    '*.log',
                    '.git/*',
                    '__pycache__/*',
                    '*.pyc',
                    '.DS_Store',
                    'Thumbs.db'
                ]
            },
            'remote': {
                'base_path': '',
                'create_dirs': True,
                'file_permissions': None
            },
            'sync': {
                'mode': 'upload_only',
                'initial_sync': False,
                'retry_attempts': 3,
                'retry_delay': 5,
                'delete_remote': False
            },
            'logging': {
                'level': 'INFO',
                'file': '',
                'max_file_size': 10,
                'backup_count': 5
            }
        }
        
        # Use provided config or default
        self.config = copy.deepcopy(config) if config else copy.deepcopy(self.default_config)
        self.site_name = site_name
        
        self.create_dialog(title)
    
    def create_dialog(self, title):
        """Create the dialog window"""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title(title)
        self.dialog.geometry("600x700")
        self.dialog.resizable(True, True)
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        
        # Center dialog on parent
        self.dialog.update_idletasks()
        x = self.parent.winfo_x() + (self.parent.winfo_width() // 2) - (600 // 2)
        y = self.parent.winfo_y() + (self.parent.winfo_height() // 2) - (700 // 2)
        self.dialog.geometry(f"600x700+{x}+{y}")
        
        # Main frame with scrollbar
        main_frame = ttk.Frame(self.dialog, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Create notebook for tabs
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # Create tabs
        self.create_general_tab()
        self.create_ftp_tab()
        self.create_local_tab()
        self.create_remote_tab()
        self.create_sync_tab()
        self.create_logging_tab()
        
        # Buttons frame
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X)
        
        ttk.Button(buttons_frame, text="Test Connection", command=self.test_connection).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(buttons_frame, text="Cancel", command=self.cancel).pack(side=tk.RIGHT)
        ttk.Button(buttons_frame, text="Save", command=self.save).pack(side=tk.RIGHT, padx=(0, 5))
        
        # Bind Enter and Escape keys
        self.dialog.bind('<Return>', lambda e: self.save())
        self.dialog.bind('<Escape>', lambda e: self.cancel())
        
        # Focus on site name field
        if hasattr(self, 'name_var'):
            self.name_entry.focus()
    
    def create_general_tab(self):
        """Create general settings tab"""
        frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(frame, text="General")
        
        # Site name
        ttk.Label(frame, text="Site Name:").grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        self.name_var = tk.StringVar(value=self.site_name)
        self.name_entry = ttk.Entry(frame, textvariable=self.name_var, width=40)
        self.name_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=(0, 5))
        
        # Description
        ttk.Label(frame, text="Description:").grid(row=1, column=0, sticky=(tk.W, tk.N), pady=(0, 5))
        self.description_text = tk.Text(frame, height=4, width=40)
        self.description_text.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=(0, 5))
        
        # Enabled checkbox
        self.enabled_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(frame, text="Enable this site", variable=self.enabled_var).grid(row=2, column=1, sticky=tk.W, pady=(5, 0))
        
        frame.columnconfigure(1, weight=1)
    
    def create_ftp_tab(self):
        """Create FTP settings tab"""
        frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(frame, text="FTP Server")
        
        # FTP Host
        ttk.Label(frame, text="Host:").grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        self.ftp_host_var = tk.StringVar(value=self.config['ftp']['host'])
        ttk.Entry(frame, textvariable=self.ftp_host_var, width=40).grid(row=0, column=1, sticky=(tk.W, tk.E), pady=(0, 5))
        
        # Port
        ttk.Label(frame, text="Port:").grid(row=1, column=0, sticky=tk.W, pady=(0, 5))
        self.ftp_port_var = tk.StringVar(value=str(self.config['ftp']['port']))
        ttk.Entry(frame, textvariable=self.ftp_port_var, width=10).grid(row=1, column=1, sticky=tk.W, pady=(0, 5))
        
        # Username
        ttk.Label(frame, text="Username:").grid(row=2, column=0, sticky=tk.W, pady=(0, 5))
        self.ftp_username_var = tk.StringVar(value=self.config['ftp']['username'])
        ttk.Entry(frame, textvariable=self.ftp_username_var, width=40).grid(row=2, column=1, sticky=(tk.W, tk.E), pady=(0, 5))
        
        # Password
        ttk.Label(frame, text="Password:").grid(row=3, column=0, sticky=tk.W, pady=(0, 5))
        self.ftp_password_var = tk.StringVar(value=self.config['ftp']['password'])
        ttk.Entry(frame, textvariable=self.ftp_password_var, show="*", width=40).grid(row=3, column=1, sticky=(tk.W, tk.E), pady=(0, 5))
        
        # Passive mode
        self.ftp_passive_var = tk.BooleanVar(value=self.config['ftp']['passive'])
        ttk.Checkbutton(frame, text="Use passive mode (recommended)", variable=self.ftp_passive_var).grid(row=4, column=1, sticky=tk.W, pady=(5, 0))
        
        # Timeout
        ttk.Label(frame, text="Timeout (seconds):").grid(row=5, column=0, sticky=tk.W, pady=(5, 0))
        self.ftp_timeout_var = tk.StringVar(value=str(self.config['ftp']['timeout']))
        ttk.Entry(frame, textvariable=self.ftp_timeout_var, width=10).grid(row=5, column=1, sticky=tk.W, pady=(5, 0))
        
        frame.columnconfigure(1, weight=1)
    
    def create_local_tab(self):
        """Create local settings tab"""
        frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(frame, text="Local Settings")
        
        # Watch path
        ttk.Label(frame, text="Watch Path:").grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        
        path_frame = ttk.Frame(frame)
        path_frame.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=(0, 5))
        path_frame.columnconfigure(0, weight=1)
        
        self.watch_path_var = tk.StringVar(value=self.config['local']['watch_path'])
        ttk.Entry(path_frame, textvariable=self.watch_path_var).grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 5))
        ttk.Button(path_frame, text="Browse", command=self.browse_watch_path).grid(row=0, column=1)
        
        # Exclude patterns
        ttk.Label(frame, text="Exclude Patterns:").grid(row=1, column=0, sticky=(tk.W, tk.N), pady=(10, 5))
        
        patterns_frame = ttk.Frame(frame)
        patterns_frame.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 5))
        patterns_frame.columnconfigure(0, weight=1)
        patterns_frame.rowconfigure(0, weight=1)
        
        # Patterns listbox with scrollbar
        listbox_frame = ttk.Frame(patterns_frame)
        listbox_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        listbox_frame.columnconfigure(0, weight=1)
        listbox_frame.rowconfigure(0, weight=1)
        
        self.patterns_listbox = tk.Listbox(listbox_frame, height=8)
        self.patterns_listbox.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        patterns_scrollbar = ttk.Scrollbar(listbox_frame, orient=tk.VERTICAL, command=self.patterns_listbox.yview)
        patterns_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.patterns_listbox.configure(yscrollcommand=patterns_scrollbar.set)
        
        # Load existing patterns
        for pattern in self.config['local']['exclude_patterns']:
            self.patterns_listbox.insert(tk.END, pattern)
        
        # Patterns buttons
        patterns_buttons = ttk.Frame(patterns_frame)
        patterns_buttons.grid(row=1, column=0, sticky=tk.W, pady=(5, 0))
        
        ttk.Button(patterns_buttons, text="Add", command=self.add_pattern).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(patterns_buttons, text="Remove", command=self.remove_pattern).pack(side=tk.LEFT)
        
        frame.columnconfigure(1, weight=1)
        frame.rowconfigure(1, weight=1)
    
    def create_remote_tab(self):
        """Create remote settings tab"""
        frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(frame, text="Remote Settings")
        
        # Base path
        ttk.Label(frame, text="Base Path:").grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        self.remote_base_path_var = tk.StringVar(value=self.config['remote']['base_path'])
        ttk.Entry(frame, textvariable=self.remote_base_path_var, width=40).grid(row=0, column=1, sticky=(tk.W, tk.E), pady=(0, 5))
        
        # Create directories
        self.remote_create_dirs_var = tk.BooleanVar(value=self.config['remote']['create_dirs'])
        ttk.Checkbutton(frame, text="Create directories if they don't exist", variable=self.remote_create_dirs_var).grid(row=1, column=1, sticky=tk.W, pady=(5, 0))
        
        frame.columnconfigure(1, weight=1)
    
    def create_sync_tab(self):
        """Create sync settings tab"""
        frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(frame, text="Sync Settings")
        
        # Sync mode
        ttk.Label(frame, text="Sync Mode:").grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        self.sync_mode_var = tk.StringVar(value=self.config['sync']['mode'])
        mode_combo = ttk.Combobox(frame, textvariable=self.sync_mode_var, values=['upload_only'], state="readonly")
        mode_combo.grid(row=0, column=1, sticky=tk.W, pady=(0, 5))
        
        # Initial sync
        self.sync_initial_var = tk.BooleanVar(value=self.config['sync']['initial_sync'])
        ttk.Checkbutton(frame, text="Sync existing files on startup", variable=self.sync_initial_var).grid(row=1, column=1, sticky=tk.W, pady=(5, 0))
        
        # Delete remote
        self.sync_delete_remote_var = tk.BooleanVar(value=self.config['sync']['delete_remote'])
        ttk.Checkbutton(frame, text="Delete remote files when local files are deleted", variable=self.sync_delete_remote_var).grid(row=2, column=1, sticky=tk.W, pady=(5, 0))
        
        # Retry attempts
        ttk.Label(frame, text="Retry Attempts:").grid(row=3, column=0, sticky=tk.W, pady=(10, 5))
        self.sync_retry_attempts_var = tk.StringVar(value=str(self.config['sync']['retry_attempts']))
        ttk.Entry(frame, textvariable=self.sync_retry_attempts_var, width=10).grid(row=3, column=1, sticky=tk.W, pady=(10, 5))
        
        # Retry delay
        ttk.Label(frame, text="Retry Delay (seconds):").grid(row=4, column=0, sticky=tk.W, pady=(0, 5))
        self.sync_retry_delay_var = tk.StringVar(value=str(self.config['sync']['retry_delay']))
        ttk.Entry(frame, textvariable=self.sync_retry_delay_var, width=10).grid(row=4, column=1, sticky=tk.W, pady=(0, 5))
        
        frame.columnconfigure(1, weight=1)
    
    def create_logging_tab(self):
        """Create logging settings tab"""
        frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(frame, text="Logging")
        
        # Log level
        ttk.Label(frame, text="Log Level:").grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        self.log_level_var = tk.StringVar(value=self.config['logging']['level'])
        level_combo = ttk.Combobox(frame, textvariable=self.log_level_var, 
                                  values=['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'], 
                                  state="readonly")
        level_combo.grid(row=0, column=1, sticky=tk.W, pady=(0, 5))
        
        # Log file
        ttk.Label(frame, text="Log File:").grid(row=1, column=0, sticky=tk.W, pady=(0, 5))
        self.log_file_var = tk.StringVar(value=self.config['logging']['file'])
        ttk.Entry(frame, textvariable=self.log_file_var, width=40).grid(row=1, column=1, sticky=(tk.W, tk.E), pady=(0, 5))
        
        # Max file size
        ttk.Label(frame, text="Max File Size (MB):").grid(row=2, column=0, sticky=tk.W, pady=(0, 5))
        self.log_max_size_var = tk.StringVar(value=str(self.config['logging']['max_file_size']))
        ttk.Entry(frame, textvariable=self.log_max_size_var, width=10).grid(row=2, column=1, sticky=tk.W, pady=(0, 5))
        
        # Backup count
        ttk.Label(frame, text="Backup Count:").grid(row=3, column=0, sticky=tk.W, pady=(0, 5))
        self.log_backup_count_var = tk.StringVar(value=str(self.config['logging']['backup_count']))
        ttk.Entry(frame, textvariable=self.log_backup_count_var, width=10).grid(row=3, column=1, sticky=tk.W, pady=(0, 5))
        
        frame.columnconfigure(1, weight=1)
    
    def browse_watch_path(self):
        """Browse for watch path"""
        path = filedialog.askdirectory(title="Select Watch Directory")
        if path:
            self.watch_path_var.set(path)
    
    def add_pattern(self):
        """Add exclude pattern"""
        pattern = tk.simpledialog.askstring("Add Pattern", "Enter exclude pattern (e.g., *.tmp):")
        if pattern:
            self.patterns_listbox.insert(tk.END, pattern)
    
    def remove_pattern(self):
        """Remove selected exclude pattern"""
        selection = self.patterns_listbox.curselection()
        if selection:
            self.patterns_listbox.delete(selection[0])
    
    def test_connection(self):
        """Test FTP connection"""
        try:
            # Validate and get current config
            config = self.get_config()
            
            # Import here to avoid circular imports
            from ..ftp_client import FTPClient
            from ..logger import Logger
            
            # Create temporary logger
            logger_config = {'logging': {'level': 'INFO', 'file': '', 'max_file_size': 10, 'backup_count': 5}}
            logger_manager = Logger(logger_config)
            logger = logger_manager.get_logger()
            
            # Create temporary config manager
            class TempConfig:
                def __init__(self, config):
                    self.config = config
                def get_ftp_config(self):
                    return self.config['ftp']
                def get_remote_config(self):
                    return self.config.get('remote', {})
                def get_sync_config(self):
                    return self.config['sync']
            
            temp_config = TempConfig(config)
            
            # Test connection
            ftp_client = FTPClient(temp_config, logger)
            
            if ftp_client.connect():
                ftp_client.disconnect()
                messagebox.showinfo("Success", "FTP connection test successful!")
            else:
                messagebox.showerror("Error", "FTP connection test failed!")
                
        except Exception as e:
            messagebox.showerror("Error", f"Connection test failed: {e}")
    
    def get_config(self):
        """Get configuration from form"""
        # Get exclude patterns from listbox
        exclude_patterns = []
        for i in range(self.patterns_listbox.size()):
            exclude_patterns.append(self.patterns_listbox.get(i))
        
        config = {
            'ftp': {
                'host': self.ftp_host_var.get().strip(),
                'port': int(self.ftp_port_var.get() or 21),
                'username': self.ftp_username_var.get().strip(),
                'password': self.ftp_password_var.get(),
                'passive': self.ftp_passive_var.get(),
                'timeout': int(self.ftp_timeout_var.get() or 30)
            },
            'local': {
                'watch_path': self.watch_path_var.get().strip(),
                'exclude_patterns': exclude_patterns
            },
            'remote': {
                'base_path': self.remote_base_path_var.get().strip(),
                'create_dirs': self.remote_create_dirs_var.get(),
                'file_permissions': None
            },
            'sync': {
                'mode': self.sync_mode_var.get(),
                'initial_sync': self.sync_initial_var.get(),
                'retry_attempts': int(self.sync_retry_attempts_var.get() or 3),
                'retry_delay': int(self.sync_retry_delay_var.get() or 5),
                'delete_remote': self.sync_delete_remote_var.get()
            },
            'logging': {
                'level': self.log_level_var.get(),
                'file': self.log_file_var.get().strip(),
                'max_file_size': int(self.log_max_size_var.get() or 10),
                'backup_count': int(self.log_backup_count_var.get() or 5)
            }
        }
        
        return config
    
    def validate_config(self, config):
        """Validate configuration"""
        errors = []
        
        # Validate site name
        name = self.name_var.get().strip()
        if not name:
            errors.append("Site name is required")
        
        # Validate FTP settings
        if not config['ftp']['host']:
            errors.append("FTP host is required")
        if not config['ftp']['username']:
            errors.append("FTP username is required")
        if not config['ftp']['password']:
            errors.append("FTP password is required")
        
        # Validate local settings
        if not config['local']['watch_path']:
            errors.append("Watch path is required")
        else:
            # Check if path exists or can be created
            try:
                watch_path = Path(config['local']['watch_path'])
                watch_path.mkdir(parents=True, exist_ok=True)
            except Exception as e:
                errors.append(f"Invalid watch path: {e}")
        
        return errors
    
    def save(self):
        """Save configuration"""
        try:
            config = self.get_config()
            errors = self.validate_config(config)
            
            if errors:
                messagebox.showerror("Validation Error", "\n".join(errors))
                return
            
            self.result = {
                'name': self.name_var.get().strip(),
                'config': config
            }
            
            self.dialog.destroy()
            
        except ValueError as e:
            messagebox.showerror("Error", f"Invalid input: {e}")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save configuration: {e}")
    
    def cancel(self):
        """Cancel dialog"""
        self.result = None
        self.dialog.destroy()


# Import simpledialog for add_pattern method
import tkinter.simpledialog
