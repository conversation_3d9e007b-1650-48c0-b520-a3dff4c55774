"""
Activity log viewer for FTP AutoSync
"""

import tkinter as tk
from tkinter import ttk
from datetime import datetime
from typing import List, Dict


class ActivityViewer(ttk.Frame):
    """Widget for displaying activity logs in a blog-like format"""
    
    def __init__(self, parent):
        super().__init__(parent)
        self.create_widgets()
        self.logs = []
    
    def create_widgets(self):
        """Create the activity viewer widgets"""
        # Create text widget with scrollbar
        text_frame = ttk.Frame(self)
        text_frame.pack(fill=tk.BOTH, expand=True)
        
        self.text_widget = tk.Text(
            text_frame,
            wrap=tk.WORD,
            font=('SF Mono', 11),
            bg='#f8f9fa',
            fg='#212529',
            selectbackground='#007acc',
            relief=tk.FLAT,
            borderwidth=0,
            padx=10,
            pady=10
        )
        self.text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # Scrollbar
        scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=self.text_widget.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.text_widget.configure(yscrollcommand=scrollbar.set)
        
        # Configure text tags for different log levels
        self.setup_text_tags()
        
        # Make text widget read-only
        self.text_widget.configure(state=tk.DISABLED)
    
    def setup_text_tags(self):
        """Setup text tags for different log levels and formatting"""
        # Log level colors
        self.text_widget.tag_configure('INFO', foreground='#28a745', font=('SF Mono', 11, 'bold'))
        self.text_widget.tag_configure('WARNING', foreground='#ffc107', font=('SF Mono', 11, 'bold'))
        self.text_widget.tag_configure('ERROR', foreground='#dc3545', font=('SF Mono', 11, 'bold'))
        self.text_widget.tag_configure('DEBUG', foreground='#6c757d', font=('SF Mono', 11, 'bold'))
        self.text_widget.tag_configure('CRITICAL', foreground='#dc3545', font=('SF Mono', 11, 'bold'), background='#f8d7da')
        
        # Other formatting
        self.text_widget.tag_configure('timestamp', foreground='#6c757d', font=('SF Mono', 10))
        self.text_widget.tag_configure('site_name', foreground='#007acc', font=('SF Mono', 11, 'bold'))
        self.text_widget.tag_configure('message', foreground='#212529', font=('SF Mono', 11))
        self.text_widget.tag_configure('details', foreground='#6c757d', font=('SF Mono', 10, 'italic'))
        self.text_widget.tag_configure('separator', foreground='#dee2e6')
    
    def update_logs(self, logs: List[Dict]):
        """Update the activity log display"""
        self.logs = logs
        self.refresh_display()
    
    def refresh_display(self):
        """Refresh the display with current logs"""
        # Enable text widget for editing
        self.text_widget.configure(state=tk.NORMAL)
        
        # Clear existing content
        self.text_widget.delete(1.0, tk.END)
        
        if not self.logs:
            self.text_widget.insert(tk.END, "No activity logs to display.\n\n")
            self.text_widget.insert(tk.END, "Start monitoring sites to see activity here.")
            self.text_widget.configure(state=tk.DISABLED)
            return
        
        # Add logs in reverse chronological order (newest first)
        for i, log in enumerate(self.logs):
            self.add_log_entry(log, i == 0)
        
        # Disable text widget
        self.text_widget.configure(state=tk.DISABLED)
        
        # Scroll to top
        self.text_widget.see(1.0)
    
    def add_log_entry(self, log: Dict, is_first: bool = False):
        """Add a single log entry to the display"""
        if not is_first:
            # Add separator line
            self.text_widget.insert(tk.END, "─" * 80 + "\n", 'separator')
        
        # Format timestamp
        timestamp = log.get('timestamp', '')
        if timestamp:
            try:
                # Parse timestamp and format it nicely
                dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                formatted_time = dt.strftime('%Y-%m-%d %H:%M:%S')
            except:
                formatted_time = timestamp
        else:
            formatted_time = 'Unknown time'
        
        # Add timestamp
        self.text_widget.insert(tk.END, f"🕐 {formatted_time}", 'timestamp')
        
        # Add site name
        site_name = log.get('site_name', 'Unknown Site')
        self.text_widget.insert(tk.END, f" • {site_name}", 'site_name')
        
        # Add log level with appropriate icon
        level = log.get('level', 'INFO')
        level_icon = self.get_level_icon(level)
        self.text_widget.insert(tk.END, f" • {level_icon} {level}", level)
        self.text_widget.insert(tk.END, "\n")
        
        # Add message
        message = log.get('message', '')
        if message:
            self.text_widget.insert(tk.END, f"📝 {message}", 'message')
            self.text_widget.insert(tk.END, "\n")
        
        # Add details if available
        details = log.get('details', '')
        if details:
            self.text_widget.insert(tk.END, f"   ℹ️  {details}", 'details')
            self.text_widget.insert(tk.END, "\n")
        
        # Add extra spacing
        self.text_widget.insert(tk.END, "\n")
    
    def get_level_icon(self, level: str) -> str:
        """Get emoji icon for log level"""
        icons = {
            'INFO': '✅',
            'WARNING': '⚠️',
            'ERROR': '❌',
            'DEBUG': '🔍',
            'CRITICAL': '🚨'
        }
        return icons.get(level, '📋')
    
    def clear(self):
        """Clear all logs from display"""
        self.text_widget.configure(state=tk.NORMAL)
        self.text_widget.delete(1.0, tk.END)
        self.text_widget.configure(state=tk.DISABLED)
        self.logs = []
    
    def scroll_to_bottom(self):
        """Scroll to the bottom of the log"""
        self.text_widget.see(tk.END)
    
    def scroll_to_top(self):
        """Scroll to the top of the log"""
        self.text_widget.see(1.0)
    
    def search_logs(self, query: str):
        """Search for text in logs (basic implementation)"""
        if not query:
            return
        
        # Remove existing search highlights
        self.text_widget.tag_remove('search_highlight', 1.0, tk.END)
        
        # Configure search highlight tag
        self.text_widget.tag_configure('search_highlight', background='yellow', foreground='black')
        
        # Search for query
        start_pos = 1.0
        while True:
            pos = self.text_widget.search(query, start_pos, tk.END, nocase=True)
            if not pos:
                break
            
            end_pos = f"{pos}+{len(query)}c"
            self.text_widget.tag_add('search_highlight', pos, end_pos)
            start_pos = end_pos
    
    def export_logs(self, filename: str):
        """Export logs to a text file"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write("FTP AutoSync Activity Log Export\n")
                f.write("=" * 50 + "\n\n")
                
                for log in self.logs:
                    timestamp = log.get('timestamp', 'Unknown time')
                    site_name = log.get('site_name', 'Unknown Site')
                    level = log.get('level', 'INFO')
                    message = log.get('message', '')
                    details = log.get('details', '')
                    
                    f.write(f"[{timestamp}] {level} - {site_name}\n")
                    f.write(f"Message: {message}\n")
                    if details:
                        f.write(f"Details: {details}\n")
                    f.write("\n" + "-" * 50 + "\n\n")
                    
            return True
        except Exception as e:
            return False
