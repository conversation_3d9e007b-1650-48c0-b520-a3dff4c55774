"""
Main window for FTP AutoSync macOS UI
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional

from ..site_manager import SiteManager
from .site_config_dialog import SiteConfigDialog
from .activity_viewer import ActivityViewer


class MainWindow:
    """Main application window"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.site_manager = SiteManager()
        self.refresh_timer = None
        self.recent_configs = []
        self.site_id_map = {}  # Map treeview items to site IDs

        self.setup_window()
        self.create_widgets()
        self.setup_callbacks()

        # Auto-load config files on startup
        self.auto_load_configs()

        self.refresh_data()

        # Start auto-refresh
        self.start_auto_refresh()
    
    def setup_window(self):
        """Setup main window properties"""
        self.root.title("FTP AutoSync - Multi-Site Manager")
        self.root.geometry("1200x800")
        self.root.minsize(800, 600)
        
        # macOS specific styling
        try:
            # Try to use native macOS appearance
            self.root.tk.call('tk', 'scaling', 1.0)
            style = ttk.Style()
            style.theme_use('aqua')
        except:
            pass
        
        # Center window on screen
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (1200 // 2)
        y = (self.root.winfo_screenheight() // 2) - (800 // 2)
        self.root.geometry(f"1200x800+{x}+{y}")

        # Create menu bar
        self.create_menu_bar()

    def create_menu_bar(self):
        """Create menu bar with config file options"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)

        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="File", menu=file_menu)

        # Config submenu
        config_menu = tk.Menu(file_menu, tearoff=0)
        file_menu.add_cascade(label="Configuration", menu=config_menu)

        config_menu.add_command(label="Load Config File...", command=self.load_config_file, accelerator="Cmd+O")
        config_menu.add_command(label="Auto-Load config.yaml", command=self.auto_load_config_yaml)
        config_menu.add_command(label="Reload All Configs", command=self.reload_all_configs)
        config_menu.add_separator()

        # Recent configs submenu
        self.recent_menu = tk.Menu(config_menu, tearoff=0)
        config_menu.add_cascade(label="Recent Configs", menu=self.recent_menu)
        self.update_recent_menu()

        file_menu.add_separator()
        file_menu.add_command(label="Export All Sites...", command=self.export_all_sites)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.on_closing, accelerator="Cmd+Q")

        # Sites menu
        sites_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Sites", menu=sites_menu)

        sites_menu.add_command(label="Add New Site", command=self.add_site, accelerator="Cmd+N")
        sites_menu.add_command(label="Edit Selected Site", command=self.edit_site, accelerator="Cmd+E")
        sites_menu.add_command(label="Delete Selected Site", command=self.delete_site, accelerator="Delete")
        sites_menu.add_separator()
        sites_menu.add_command(label="Start Selected Site", command=self.start_site, accelerator="Cmd+R")
        sites_menu.add_command(label="Stop Selected Site", command=self.stop_site, accelerator="Cmd+S")
        sites_menu.add_separator()
        sites_menu.add_command(label="Start All Sites", command=self.start_all_sites, accelerator="Cmd+Shift+R")
        sites_menu.add_command(label="Stop All Sites", command=self.stop_all_sites, accelerator="Cmd+Shift+S")

        # Bulk Operations menu
        bulk_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Bulk Operations", menu=bulk_menu)

        bulk_menu.add_command(label="📤 Upload All Files", command=self.bulk_upload_all)
        bulk_menu.add_command(label="📥 Download All Files", command=self.bulk_download_all)
        bulk_menu.add_command(label="🔄 Sync All Sites", command=self.sync_all_sites)

        # Bind keyboard shortcuts
        self.root.bind('<Command-o>', lambda e: self.load_config_file())
        self.root.bind('<Command-n>', lambda e: self.add_site())
        self.root.bind('<Command-e>', lambda e: self.edit_site())
        self.root.bind('<Command-r>', lambda e: self.start_site())
        self.root.bind('<Command-s>', lambda e: self.stop_site())
        self.root.bind('<Command-Shift-R>', lambda e: self.start_all_sites())
        self.root.bind('<Command-Shift-S>', lambda e: self.stop_all_sites())
        self.root.bind('<Command-q>', lambda e: self.on_closing())
        self.root.bind('<Delete>', lambda e: self.delete_site())

        # Setup drag and drop for config files
        self.setup_drag_drop()

    def setup_drag_drop(self):
        """Setup drag and drop for config files"""
        try:
            # Enable drag and drop on the main window
            self.root.drop_target_register('DND_Files')
            self.root.dnd_bind('<<Drop>>', self.on_file_drop)
        except:
            # Drag and drop not available on this platform
            pass

    def on_file_drop(self, event):
        """Handle dropped files"""
        try:
            files = self.root.tk.splitlist(event.data)
            config_files = []

            for file_path in files:
                file_path = Path(file_path)
                if file_path.suffix.lower() in ['.yaml', '.yml']:
                    config_files.append(file_path)

            if config_files:
                self.load_dropped_configs(config_files)
            else:
                messagebox.showwarning("Warning", "Please drop YAML configuration files (.yaml or .yml)")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to process dropped files: {e}")

    def load_dropped_configs(self, config_files):
        """Load dropped config files"""
        loaded_count = 0
        failed_files = []

        for config_file in config_files:
            try:
                site_id = self.site_manager.import_legacy_config(str(config_file))
                site = self.site_manager.get_site(site_id)
                self.add_to_recent_configs(str(config_file))
                loaded_count += 1
            except Exception as e:
                failed_files.append(f"{config_file.name}: {e}")

        self.refresh_data()

        # Show results
        if loaded_count > 0:
            message = f"Successfully loaded {loaded_count} configuration file(s)"
            if failed_files:
                message += f"\n\nFailed to load:\n" + "\n".join(failed_files)
            messagebox.showinfo("Config Files Loaded", message)
            self.status_label.config(text=f"Loaded {loaded_count} config files via drag & drop")
        else:
            message = "Failed to load configuration files:\n" + "\n".join(failed_files)
            messagebox.showerror("Error", message)

    def create_widgets(self):
        """Create main UI widgets"""
        # Main container
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(1, weight=1)
        
        # Title and statistics
        self.create_header(main_frame)
        
        # Left panel - Sites list and controls
        self.create_sites_panel(main_frame)
        
        # Right panel - Activity log
        self.create_activity_panel(main_frame)
        
        # Status bar
        self.create_status_bar(main_frame)
    
    def create_header(self, parent):
        """Create header with title and statistics"""
        header_frame = ttk.Frame(parent)
        header_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        header_frame.columnconfigure(1, weight=1)
        
        # Title and config status
        title_frame = ttk.Frame(header_frame)
        title_frame.grid(row=0, column=0, sticky=tk.W)

        title_label = ttk.Label(title_frame, text="FTP AutoSync", font=('SF Pro Display', 24, 'bold'))
        title_label.pack(side=tk.LEFT)

        # Config status indicator
        self.config_status_label = ttk.Label(title_frame, text="", font=('SF Pro Display', 10), foreground='green')
        self.config_status_label.pack(side=tk.LEFT, padx=(10, 0))
        
        # Statistics frame
        stats_frame = ttk.Frame(header_frame)
        stats_frame.grid(row=0, column=1, sticky=tk.E)
        
        # Statistics labels
        self.stats_labels = {}
        stats_info = [
            ('total_sites', 'Total Sites'),
            ('active_sites', 'Active'),
            ('total_files_synced', 'Files Synced'),
            ('total_errors', 'Errors')
        ]
        
        for i, (key, label) in enumerate(stats_info):
            frame = ttk.Frame(stats_frame)
            frame.grid(row=0, column=i, padx=(10, 0))
            
            value_label = ttk.Label(frame, text="0", font=('SF Pro Display', 16, 'bold'))
            value_label.pack()
            
            desc_label = ttk.Label(frame, text=label, font=('SF Pro Display', 10))
            desc_label.pack()
            
            self.stats_labels[key] = value_label
    
    def create_sites_panel(self, parent):
        """Create sites management panel"""
        sites_frame = ttk.LabelFrame(parent, text="Sites", padding="10")
        sites_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 5))
        sites_frame.columnconfigure(0, weight=1)
        sites_frame.rowconfigure(2, weight=1)
        
        # Toolbar
        toolbar_frame = ttk.Frame(sites_frame)
        toolbar_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # Site management buttons
        ttk.Button(toolbar_frame, text="Add Site", command=self.add_site).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar_frame, text="Edit", command=self.edit_site).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar_frame, text="Delete", command=self.delete_site).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar_frame, text="Import Config", command=self.import_config).pack(side=tk.LEFT, padx=(0, 5))

        ttk.Separator(toolbar_frame, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=10)

        # Individual site controls
        ttk.Button(toolbar_frame, text="Start", command=self.start_site).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar_frame, text="Stop", command=self.stop_site).pack(side=tk.LEFT, padx=(0, 5))

        ttk.Separator(toolbar_frame, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=10)

        # Bulk operations
        ttk.Button(toolbar_frame, text="Start All", command=self.start_all_sites).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar_frame, text="Stop All", command=self.stop_all_sites).pack(side=tk.LEFT, padx=(0, 5))

        # Second toolbar row for bulk operations
        toolbar_frame2 = ttk.Frame(sites_frame)
        toolbar_frame2.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        ttk.Label(toolbar_frame2, text="Bulk Operations:", font=('SF Pro Display', 10, 'bold')).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(toolbar_frame2, text="📤 Upload All", command=self.bulk_upload_all).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar_frame2, text="📥 Download All", command=self.bulk_download_all).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar_frame2, text="🔄 Sync All", command=self.sync_all_sites).pack(side=tk.LEFT, padx=(0, 5))

        # Sites treeview
        columns = ('name', 'status', 'host', 'watch_path', 'last_sync', 'files_synced', 'errors')
        self.sites_tree = ttk.Treeview(sites_frame, columns=columns, show='headings', height=15)
        
        # Configure columns
        column_configs = {
            'name': ('Site Name', 150),
            'status': ('Status', 80),
            'host': ('FTP Host', 120),
            'watch_path': ('Watch Path', 200),
            'last_sync': ('Last Sync', 120),
            'files_synced': ('Files', 60),
            'errors': ('Errors', 60)
        }
        
        for col, (heading, width) in column_configs.items():
            self.sites_tree.heading(col, text=heading)
            self.sites_tree.column(col, width=width, minwidth=50)
        
        self.sites_tree.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Scrollbar for treeview
        sites_scrollbar = ttk.Scrollbar(sites_frame, orient=tk.VERTICAL, command=self.sites_tree.yview)
        sites_scrollbar.grid(row=2, column=1, sticky=(tk.N, tk.S))
        self.sites_tree.configure(yscrollcommand=sites_scrollbar.set)
        
        # Bind double-click to edit
        self.sites_tree.bind('<Double-1>', lambda e: self.edit_site())
    
    def create_activity_panel(self, parent):
        """Create activity log panel"""
        activity_frame = ttk.LabelFrame(parent, text="Activity Log", padding="10")
        activity_frame.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(5, 0))
        activity_frame.columnconfigure(0, weight=1)
        activity_frame.rowconfigure(1, weight=1)
        
        # Activity toolbar
        activity_toolbar = ttk.Frame(activity_frame)
        activity_toolbar.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Button(activity_toolbar, text="Clear Log", command=self.clear_activity_log).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(activity_toolbar, text="Export Log", command=self.export_activity_log).pack(side=tk.LEFT, padx=(0, 5))
        
        # Filter frame
        filter_frame = ttk.Frame(activity_toolbar)
        filter_frame.pack(side=tk.RIGHT)
        
        ttk.Label(filter_frame, text="Filter:").pack(side=tk.LEFT, padx=(0, 5))
        
        self.filter_var = tk.StringVar(value="All Sites")
        self.filter_combo = ttk.Combobox(filter_frame, textvariable=self.filter_var, width=15, state="readonly")
        self.filter_combo.pack(side=tk.LEFT)
        self.filter_combo.bind('<<ComboboxSelected>>', self.on_filter_changed)
        
        # Activity viewer
        self.activity_viewer = ActivityViewer(activity_frame)
        self.activity_viewer.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
    
    def create_status_bar(self, parent):
        """Create enhanced status bar"""
        status_frame = ttk.Frame(parent)
        status_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        status_frame.columnconfigure(1, weight=1)

        # Main status
        self.status_label = ttk.Label(status_frame, text="🚀 Ready - FTP AutoSync Pro", font=('SF Pro Display', 10))
        self.status_label.grid(row=0, column=0, sticky=tk.W)

        # Right side indicators
        indicators_frame = ttk.Frame(status_frame)
        indicators_frame.grid(row=0, column=1, sticky=tk.E)

        # Activity indicator
        self.activity_indicator = ttk.Label(indicators_frame, text="💤 Idle", font=('SF Pro Display', 9))
        self.activity_indicator.pack(side=tk.RIGHT, padx=(10, 0))

        # Connection status
        self.connection_status = ttk.Label(indicators_frame, text="🔴 Offline", font=('SF Pro Display', 9))
        self.connection_status.pack(side=tk.RIGHT, padx=(10, 0))

        # Last update time
        self.last_update_label = ttk.Label(indicators_frame, text="", font=('SF Pro Display', 9))
        self.last_update_label.pack(side=tk.RIGHT, padx=(10, 0))
    
    def setup_callbacks(self):
        """Setup site manager callbacks"""
        self.site_manager.add_status_callback(self.on_site_status_changed)
        self.site_manager.add_log_callback(self.on_log_entry)
    
    def on_site_status_changed(self, site_id: str, status: str):
        """Handle site status changes"""
        # Update UI in main thread
        self.root.after(0, self.refresh_sites_list)
    
    def on_log_entry(self, site_id: str, level: str, message: str, details: str = None):
        """Handle new log entries"""
        # Update activity viewer in main thread
        self.root.after(0, self.refresh_activity_log)
    
    def refresh_data(self):
        """Refresh all data"""
        self.refresh_statistics()
        self.refresh_sites_list()
        self.refresh_activity_log()
        self.refresh_filter_combo()

        # Update status indicators
        sites = self.site_manager.get_all_sites()
        running_sites = sum(1 for site in sites if self.site_manager.get_site_status(site['id']).get('running', False))

        if running_sites > 0:
            self.update_connection_status(True, running_sites)
            self.update_activity_indicator('syncing')
        else:
            self.update_connection_status(False)
            self.update_activity_indicator('idle')

        # Update last refresh time
        self.last_update_label.config(text=f"Last updated: {datetime.now().strftime('%H:%M:%S')}")
    
    def refresh_statistics(self):
        """Refresh statistics display"""
        try:
            stats = self.site_manager.get_statistics()
            for key, label in self.stats_labels.items():
                value = stats.get(key, 0)
                label.config(text=str(value))
        except Exception as e:
            self.status_label.config(text=f"Error refreshing statistics: {e}")
    
    def refresh_sites_list(self):
        """Refresh sites list"""
        try:
            # Clear existing items and mapping
            for item in self.sites_tree.get_children():
                self.sites_tree.delete(item)
            self.site_id_map.clear()

            # Add sites
            sites = self.site_manager.get_all_sites()
            for site in sites:
                status_info = self.site_manager.get_site_status(site['id'])

                # Format last sync time
                last_sync = status_info.get('last_sync', '')
                if last_sync:
                    try:
                        dt = datetime.fromisoformat(last_sync.replace('Z', '+00:00'))
                        last_sync = dt.strftime('%m/%d %H:%M')
                    except:
                        pass

                # Get status with color coding
                status = status_info.get('status', 'stopped')
                if status_info.get('running', False):
                    status = '🟢 Running'
                elif status == 'error':
                    status = '🔴 Error'
                elif status == 'stopped':
                    status = '⚪ Stopped'
                else:
                    status = f'⚫ {status.title()}'

                values = (
                    site['name'],
                    status,
                    site['config']['ftp']['host'],
                    site['config']['local']['watch_path'],
                    last_sync,
                    status_info.get('files_synced', 0),
                    status_info.get('errors_count', 0)
                )

                item = self.sites_tree.insert('', tk.END, values=values)
                # Store site ID in mapping
                self.site_id_map[item] = site['id']

        except Exception as e:
            self.status_label.config(text=f"Error refreshing sites: {e}")
    
    def refresh_activity_log(self):
        """Refresh activity log"""
        try:
            # Get filter value
            filter_site = self.filter_var.get()
            site_id = None
            
            if filter_site != "All Sites":
                # Find site ID by name
                sites = self.site_manager.get_all_sites()
                for site in sites:
                    if site['name'] == filter_site:
                        site_id = site['id']
                        break
            
            logs = self.site_manager.get_activity_logs(site_id, limit=200)
            self.activity_viewer.update_logs(logs)
            
        except Exception as e:
            self.status_label.config(text=f"Error refreshing activity log: {e}")
    
    def refresh_filter_combo(self):
        """Refresh filter combo box"""
        try:
            sites = self.site_manager.get_all_sites()
            site_names = ["All Sites"] + [site['name'] for site in sites]
            self.filter_combo['values'] = site_names
        except Exception as e:
            pass
    
    def start_auto_refresh(self):
        """Start automatic refresh timer"""
        def auto_refresh():
            if self.root.winfo_exists():
                self.refresh_data()
                self.refresh_timer = self.root.after(5000, auto_refresh)  # Refresh every 5 seconds
        
        auto_refresh()
    
    def get_selected_site_id(self) -> Optional[str]:
        """Get selected site ID from treeview"""
        selection = self.sites_tree.selection()
        if selection:
            item = selection[0]
            return self.site_id_map.get(item)
        return None
    
    def add_site(self):
        """Add new site"""
        dialog = SiteConfigDialog(self.root, "Add New Site")
        if dialog.result:
            try:
                site_id = self.site_manager.create_site(dialog.result['name'], dialog.result['config'])
                self.refresh_data()
                self.status_label.config(text=f"Site '{dialog.result['name']}' added successfully")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to add site: {e}")
    
    def edit_site(self):
        """Edit selected site"""
        try:
            site_id = self.get_selected_site_id()
            if not site_id:
                messagebox.showwarning("Warning", "Please select a site to edit")
                return

            site = self.site_manager.get_site(site_id)
            if not site:
                messagebox.showerror("Error", "Site not found")
                return

            dialog = SiteConfigDialog(self.root, "Edit Site", site['name'], site['config'])
            if dialog.result:
                try:
                    self.site_manager.update_site(site_id, dialog.result['name'], dialog.result['config'])
                    self.refresh_data()
                    self.status_label.config(text=f"Site '{dialog.result['name']}' updated successfully")
                except Exception as e:
                    messagebox.showerror("Error", f"Failed to update site: {e}")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to edit site: {e}")
            print(f"Edit site error: {e}")  # Debug info
    
    def delete_site(self):
        """Delete selected site"""
        try:
            site_id = self.get_selected_site_id()
            if not site_id:
                messagebox.showwarning("Warning", "Please select a site to delete")
                return

            site = self.site_manager.get_site(site_id)
            if not site:
                messagebox.showerror("Error", "Site not found")
                return

            if messagebox.askyesno("Confirm Delete", f"Are you sure you want to delete site '{site['name']}'?\n\nThis will stop monitoring and remove all configuration and logs."):
                try:
                    self.site_manager.delete_site(site_id)
                    self.refresh_data()
                    self.status_label.config(text=f"Site '{site['name']}' deleted successfully")
                except Exception as e:
                    messagebox.showerror("Error", f"Failed to delete site: {e}")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to delete site: {e}")
            print(f"Delete site error: {e}")  # Debug info
    
    def start_site(self):
        """Start selected site"""
        site_id = self.get_selected_site_id()
        if not site_id:
            messagebox.showwarning("Warning", "Please select a site to start")
            return
        
        try:
            self.site_manager.start_site(site_id)
            self.refresh_data()
            site = self.site_manager.get_site(site_id)
            self.status_label.config(text=f"Site '{site['name']}' started successfully")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to start site: {e}")
    
    def stop_site(self):
        """Stop selected site"""
        site_id = self.get_selected_site_id()
        if not site_id:
            messagebox.showwarning("Warning", "Please select a site to stop")
            return
        
        try:
            self.site_manager.stop_site(site_id)
            self.refresh_data()
            site = self.site_manager.get_site(site_id)
            self.status_label.config(text=f"Site '{site['name']}' stopped successfully")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to stop site: {e}")
    
    def start_all_sites(self):
        """Start all enabled sites"""
        try:
            self.site_manager.start_all_enabled_sites()
            self.refresh_data()
            self.status_label.config(text="All enabled sites started")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to start all sites: {e}")
    
    def stop_all_sites(self):
        """Stop all running sites"""
        if messagebox.askyesno("Confirm", "Are you sure you want to stop all running sites?"):
            try:
                self.site_manager.stop_all_sites()
                self.refresh_data()
                self.status_label.config(text="All sites stopped")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to stop all sites: {e}")
    
    def on_filter_changed(self, event=None):
        """Handle filter change"""
        self.refresh_activity_log()
    
    def clear_activity_log(self):
        """Clear activity log"""
        if messagebox.askyesno("Confirm", "Are you sure you want to clear the activity log?\n\nThis will remove all log entries from the database."):
            try:
                # Clear logs older than 0 days (all logs)
                self.site_manager.db.clear_old_logs(0)
                self.refresh_activity_log()
                self.status_label.config(text="Activity log cleared")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to clear activity log: {e}")
    
    def export_activity_log(self):
        """Export activity log to file"""
        try:
            filename = filedialog.asksaveasfilename(
                title="Export Activity Log",
                defaultextension=".txt",
                filetypes=[("Text files", "*.txt"), ("CSV files", "*.csv"), ("All files", "*.*")]
            )

            if filename:
                logs = self.site_manager.get_activity_logs(limit=1000)

                with open(filename, 'w', encoding='utf-8') as f:
                    f.write("FTP AutoSync Activity Log\n")
                    f.write("=" * 50 + "\n\n")

                    for log in logs:
                        f.write(f"[{log['timestamp']}] {log['level']} - {log['site_name']}: {log['message']}\n")
                        if log['details']:
                            f.write(f"  Details: {log['details']}\n")
                        f.write("\n")

                self.status_label.config(text=f"Activity log exported to {filename}")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to export activity log: {e}")

    def import_config(self):
        """Import legacy config.yaml file"""
        try:
            filename = filedialog.askopenfilename(
                title="Import Configuration File",
                defaultextension=".yaml",
                filetypes=[("YAML files", "*.yaml"), ("YAML files", "*.yml"), ("All files", "*.*")]
            )

            if filename:
                site_id = self.site_manager.import_legacy_config(filename)
                self.refresh_data()

                # Get the imported site name
                site = self.site_manager.get_site(site_id)
                site_name = site['name'] if site else 'Unknown'

                self.status_label.config(text=f"Configuration imported as '{site_name}'")
                messagebox.showinfo("Success", f"Configuration imported successfully as '{site_name}'")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to import configuration: {e}")

    def bulk_upload_all(self):
        """Upload all files from all enabled sites"""
        sites = self.site_manager.get_all_sites()
        enabled_sites = [s for s in sites if s['enabled']]

        if not enabled_sites:
            messagebox.showwarning("No Sites", "No enabled sites found. Please enable at least one site.")
            return

        # Show detailed confirmation dialog
        site_names = [s['name'] for s in enabled_sites]
        site_list = '\n'.join([f"  • {name}" for name in site_names])

        if messagebox.askyesno("Confirm Bulk Upload",
                              f"📤 BULK UPLOAD OPERATION\n\n"
                              f"This will upload ALL files from {len(enabled_sites)} enabled site(s):\n\n"
                              f"{site_list}\n\n"
                              f"⚠️  WARNING:\n"
                              f"• This may take a long time\n"
                              f"• Will generate significant network traffic\n"
                              f"• Files will overwrite existing remote files\n\n"
                              f"Continue with bulk upload?"):

            # Create and show progress dialog
            progress_dialog = self.create_progress_dialog("Bulk Upload", "Preparing upload...")

            try:
                # Run in thread to prevent UI blocking
                import threading
                def bulk_upload_thread():
                    try:
                        # Update progress
                        self.root.after(0, lambda: self.update_progress_dialog(progress_dialog, "Connecting to sites...", 10))

                        results = self.site_manager.bulk_upload_all_sites()

                        # Update UI in main thread
                        self.root.after(0, lambda: self.close_progress_dialog(progress_dialog))
                        self.root.after(0, lambda: self._show_bulk_results("Upload", results))

                    except Exception as e:
                        self.root.after(0, lambda: self.close_progress_dialog(progress_dialog))
                        self.root.after(0, lambda: messagebox.showerror("Upload Error", f"Bulk upload failed:\n\n{e}"))

                thread = threading.Thread(target=bulk_upload_thread, daemon=True)
                thread.start()

            except Exception as e:
                self.close_progress_dialog(progress_dialog)
                messagebox.showerror("Error", f"Failed to start bulk upload: {e}")

    def bulk_download_all(self):
        """Download all files from all enabled sites"""
        # Ask for download directory
        download_dir = filedialog.askdirectory(title="Select Download Directory")
        if not download_dir:
            return

        if messagebox.askyesno("Confirm Bulk Download",
                              f"This will download ALL files from ALL enabled sites to:\n{download_dir}\n\n"
                              "This may take a long time and use significant disk space.\n\n"
                              "Are you sure you want to continue?"):
            try:
                self.status_label.config(text="Starting bulk download for all sites...")
                self.root.update()

                # Run in thread to prevent UI blocking
                import threading
                def bulk_download_thread():
                    try:
                        results = self.site_manager.bulk_download_all_sites(download_dir)

                        # Update UI in main thread
                        self.root.after(0, lambda: self._show_bulk_results("Download", results))

                    except Exception as e:
                        self.root.after(0, lambda: messagebox.showerror("Error", f"Bulk download failed: {e}"))

                thread = threading.Thread(target=bulk_download_thread, daemon=True)
                thread.start()

            except Exception as e:
                messagebox.showerror("Error", f"Failed to start bulk download: {e}")

    def sync_all_sites(self):
        """Start monitoring all enabled sites"""
        try:
            self.site_manager.start_all_enabled_sites()
            self.refresh_data()
            self.status_label.config(text="Started monitoring all enabled sites")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to start all sites: {e}")

    def _show_bulk_results(self, operation: str, results: dict):
        """Show results of bulk operation"""
        success_count = sum(1 for success in results.values() if success)
        total_count = len(results)

        result_text = f"{operation} Results:\n\n"
        result_text += f"Successful: {success_count}/{total_count}\n\n"

        for site_name, success in results.items():
            status = "✅ Success" if success else "❌ Failed"
            result_text += f"{status} - {site_name}\n"

        self.status_label.config(text=f"Bulk {operation.lower()} completed: {success_count}/{total_count} successful")
        self.refresh_data()

        # Show enhanced results dialog
        self.show_enhanced_results_dialog(operation, results, success_count, total_count)

    def show_enhanced_results_dialog(self, operation, results, success_count, total_count):
        """Show enhanced results dialog"""
        # Create custom results dialog
        dialog = tk.Toplevel(self.root)
        dialog.title(f"Bulk {operation} Results")
        dialog.geometry("600x500")
        dialog.resizable(True, True)
        dialog.transient(self.root)
        dialog.grab_set()

        # Center on parent
        dialog.update_idletasks()
        x = self.root.winfo_x() + (self.root.winfo_width() // 2) - (600 // 2)
        y = self.root.winfo_y() + (self.root.winfo_height() // 2) - (500 // 2)
        dialog.geometry(f"600x500+{x}+{y}")

        # Main frame
        main_frame = ttk.Frame(dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Header
        header_frame = ttk.Frame(main_frame)
        header_frame.pack(fill=tk.X, pady=(0, 20))

        # Success/failure indicator
        if success_count == total_count:
            icon = "🎉"
            status_text = "All operations completed successfully!"
        elif success_count > 0:
            icon = "⚠️"
            status_text = f"Partial success: {success_count}/{total_count} completed"
        else:
            icon = "❌"
            status_text = "All operations failed"

        title_label = ttk.Label(header_frame, text=f"{icon} Bulk {operation} Results",
                               font=('SF Pro Display', 18, 'bold'))
        title_label.pack()

        status_label = ttk.Label(header_frame, text=status_text,
                                font=('SF Pro Display', 12))
        status_label.pack(pady=(5, 0))

        # Summary stats
        stats_frame = ttk.LabelFrame(main_frame, text="📊 Summary", padding="15")
        stats_frame.pack(fill=tk.X, pady=(0, 20))

        stats_grid = ttk.Frame(stats_frame)
        stats_grid.pack(fill=tk.X)

        ttk.Label(stats_grid, text="Total Sites:", font=('SF Pro Display', 11, 'bold')).grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        ttk.Label(stats_grid, text=str(total_count)).grid(row=0, column=1, sticky=tk.W)

        ttk.Label(stats_grid, text="Successful:", font=('SF Pro Display', 11, 'bold')).grid(row=0, column=2, sticky=tk.W, padx=(20, 10))
        ttk.Label(stats_grid, text=str(success_count), foreground='green').grid(row=0, column=3, sticky=tk.W)

        ttk.Label(stats_grid, text="Failed:", font=('SF Pro Display', 11, 'bold')).grid(row=0, column=4, sticky=tk.W, padx=(20, 10))
        ttk.Label(stats_grid, text=str(total_count - success_count), foreground='red').grid(row=0, column=5, sticky=tk.W)

        # Results list
        results_frame = ttk.LabelFrame(main_frame, text="📋 Detailed Results", padding="15")
        results_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))

        # Create treeview for results
        columns = ('Status', 'Site Name')
        results_tree = ttk.Treeview(results_frame, columns=columns, show='headings', height=10)

        # Configure columns
        results_tree.heading('Status', text='Status')
        results_tree.heading('Site Name', text='Site Name')
        results_tree.column('Status', width=120, anchor='center')
        results_tree.column('Site Name', width=400)

        # Add scrollbar
        scrollbar = ttk.Scrollbar(results_frame, orient=tk.VERTICAL, command=results_tree.yview)
        results_tree.configure(yscrollcommand=scrollbar.set)

        # Pack treeview and scrollbar
        results_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Populate results
        for site_name, success in results.items():
            status = "✅ Success" if success else "❌ Failed"
            results_tree.insert('', tk.END, values=(status, site_name))

        # Buttons frame
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X)

        # View logs button
        def view_logs():
            dialog.destroy()
            self.show_activity_logs()

        ttk.Button(buttons_frame, text="📄 View Activity Logs", command=view_logs).pack(side=tk.LEFT)

        # Close button
        ttk.Button(buttons_frame, text="Close", command=dialog.destroy).pack(side=tk.RIGHT)

    def show_activity_logs(self):
        """Show activity logs in a separate window"""
        # Create logs window
        logs_window = tk.Toplevel(self.root)
        logs_window.title("Activity Logs")
        logs_window.geometry("800x600")
        logs_window.transient(self.root)

        # Main frame
        main_frame = ttk.Frame(logs_window, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Header
        ttk.Label(main_frame, text="📄 Activity Logs", font=('SF Pro Display', 16, 'bold')).pack(pady=(0, 10))

        # Logs text area
        logs_frame = ttk.Frame(main_frame)
        logs_frame.pack(fill=tk.BOTH, expand=True)

        logs_text = tk.Text(logs_frame, wrap=tk.WORD, font=('Monaco', 10))
        logs_scrollbar = ttk.Scrollbar(logs_frame, orient=tk.VERTICAL, command=logs_text.yview)
        logs_text.configure(yscrollcommand=logs_scrollbar.set)

        logs_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        logs_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Load and display logs
        try:
            logs = self.site_manager.get_activity_logs(limit=100)
            if logs:
                for log in logs:
                    timestamp = log['timestamp']
                    level = log['level']
                    site_name = log['site_name']
                    message = log['message']

                    log_line = f"[{timestamp}] {level} - {site_name}: {message}\n"
                    logs_text.insert(tk.END, log_line)

                    if log.get('details'):
                        logs_text.insert(tk.END, f"  Details: {log['details']}\n")
            else:
                logs_text.insert(tk.END, "No activity logs found.")
        except Exception as e:
            logs_text.insert(tk.END, f"Error loading logs: {e}")

        logs_text.config(state=tk.DISABLED)

        # Close button
        ttk.Button(main_frame, text="Close", command=logs_window.destroy).pack(pady=(10, 0))

    def auto_load_configs(self):
        """Auto-load config files on startup"""
        config_files = [
            "config.yaml",
            "config.yml",
            "ftpautosync.yaml",
            "ftpautosync.yml"
        ]

        loaded_count = 0
        for config_file in config_files:
            if Path(config_file).exists():
                try:
                    site_id = self.site_manager.import_legacy_config(config_file)
                    site = self.site_manager.get_site(site_id)
                    self.status_label.config(text=f"Auto-loaded: {config_file} as '{site['name']}'")
                    self.config_status_label.config(text=f"📁 {config_file} loaded")
                    self.add_to_recent_configs(config_file)
                    loaded_count += 1
                    break  # Only load the first found config
                except Exception as e:
                    print(f"Failed to auto-load {config_file}: {e}")

        if loaded_count == 0:
            self.status_label.config(text="No config files found for auto-loading")
            self.config_status_label.config(text="📄 No config auto-loaded")

    def load_config_file(self):
        """Load a specific config file"""
        filename = filedialog.askopenfilename(
            title="Load Configuration File",
            defaultextension=".yaml",
            filetypes=[
                ("YAML files", "*.yaml"),
                ("YAML files", "*.yml"),
                ("All files", "*.*")
            ]
        )

        if filename:
            try:
                site_id = self.site_manager.import_legacy_config(filename)
                site = self.site_manager.get_site(site_id)
                self.refresh_data()
                self.add_to_recent_configs(filename)
                self.status_label.config(text=f"Loaded: {Path(filename).name} as '{site['name']}'")
                messagebox.showinfo("Success", f"Configuration loaded successfully as '{site['name']}'")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to load configuration: {e}")

    def auto_load_config_yaml(self):
        """Auto-load config.yaml if it exists"""
        config_file = "config.yaml"
        if Path(config_file).exists():
            try:
                site_id = self.site_manager.import_legacy_config(config_file)
                site = self.site_manager.get_site(site_id)
                self.refresh_data()
                self.add_to_recent_configs(config_file)
                self.status_label.config(text=f"Loaded: {config_file} as '{site['name']}'")
                messagebox.showinfo("Success", f"config.yaml loaded as '{site['name']}'")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to load config.yaml: {e}")
        else:
            messagebox.showwarning("Warning", "config.yaml not found in current directory")

    def reload_all_configs(self):
        """Reload all recent config files"""
        if not self.recent_configs:
            messagebox.showinfo("Info", "No recent config files to reload")
            return

        loaded_count = 0
        for config_file in self.recent_configs:
            if Path(config_file).exists():
                try:
                    site_id = self.site_manager.import_legacy_config(config_file)
                    loaded_count += 1
                except Exception as e:
                    print(f"Failed to reload {config_file}: {e}")

        self.refresh_data()
        self.status_label.config(text=f"Reloaded {loaded_count} config files")
        messagebox.showinfo("Success", f"Reloaded {loaded_count} configuration files")

    def add_to_recent_configs(self, config_file):
        """Add config file to recent list"""
        config_file = str(Path(config_file).resolve())
        if config_file in self.recent_configs:
            self.recent_configs.remove(config_file)
        self.recent_configs.insert(0, config_file)
        self.recent_configs = self.recent_configs[:10]  # Keep only last 10
        self.update_recent_menu()

    def update_recent_menu(self):
        """Update recent configs menu"""
        self.recent_menu.delete(0, tk.END)

        if not self.recent_configs:
            self.recent_menu.add_command(label="(No recent configs)", state="disabled")
        else:
            for config_file in self.recent_configs:
                filename = Path(config_file).name
                self.recent_menu.add_command(
                    label=filename,
                    command=lambda f=config_file: self.load_recent_config(f)
                )

    def load_recent_config(self, config_file):
        """Load a recent config file"""
        if Path(config_file).exists():
            try:
                site_id = self.site_manager.import_legacy_config(config_file)
                site = self.site_manager.get_site(site_id)
                self.refresh_data()
                self.status_label.config(text=f"Loaded: {Path(config_file).name} as '{site['name']}'")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to load {Path(config_file).name}: {e}")
        else:
            messagebox.showerror("Error", f"Config file not found: {config_file}")
            self.recent_configs.remove(config_file)
            self.update_recent_menu()

    def export_all_sites(self):
        """Export all sites to a config file"""
        filename = filedialog.asksaveasfilename(
            title="Export All Sites",
            defaultextension=".yaml",
            filetypes=[("YAML files", "*.yaml"), ("All files", "*.*")]
        )

        if filename:
            try:
                sites = self.site_manager.get_all_sites()

                if not sites:
                    messagebox.showwarning("Warning", "No sites to export")
                    return

                # Create export data
                export_data = {
                    'sites': [],
                    'exported_at': str(datetime.now()),
                    'total_sites': len(sites)
                }

                for site in sites:
                    export_data['sites'].append({
                        'name': site['name'],
                        'enabled': site['enabled'],
                        'config': site['config']
                    })

                import yaml
                with open(filename, 'w') as f:
                    yaml.dump(export_data, f, default_flow_style=False, indent=2)

                self.status_label.config(text=f"Exported {len(sites)} sites to {Path(filename).name}")
                messagebox.showinfo("Success", f"Exported {len(sites)} sites to {Path(filename).name}")

            except Exception as e:
                messagebox.showerror("Error", f"Failed to export sites: {e}")

    def create_progress_dialog(self, title, message):
        """Create a progress dialog"""
        dialog = tk.Toplevel(self.root)
        dialog.title(title)
        dialog.geometry("400x150")
        dialog.resizable(False, False)
        dialog.transient(self.root)
        dialog.grab_set()

        # Center on parent
        dialog.update_idletasks()
        x = self.root.winfo_x() + (self.root.winfo_width() // 2) - (400 // 2)
        y = self.root.winfo_y() + (self.root.winfo_height() // 2) - (150 // 2)
        dialog.geometry(f"400x150+{x}+{y}")

        # Content frame
        content_frame = ttk.Frame(dialog, padding="20")
        content_frame.pack(fill=tk.BOTH, expand=True)

        # Title label
        title_label = ttk.Label(content_frame, text=title, font=('SF Pro Display', 14, 'bold'))
        title_label.pack(pady=(0, 10))

        # Message label
        message_label = ttk.Label(content_frame, text=message)
        message_label.pack(pady=(0, 15))

        # Progress bar
        progress_bar = ttk.Progressbar(content_frame, mode='indeterminate')
        progress_bar.pack(fill=tk.X, pady=(0, 10))
        progress_bar.start()

        # Store references
        dialog.message_label = message_label
        dialog.progress_bar = progress_bar

        return dialog

    def update_progress_dialog(self, dialog, message, progress=None):
        """Update progress dialog"""
        if dialog and dialog.winfo_exists():
            dialog.message_label.config(text=message)
            if progress is not None:
                dialog.progress_bar.config(mode='determinate', value=progress)

    def close_progress_dialog(self, dialog):
        """Close progress dialog"""
        if dialog and dialog.winfo_exists():
            dialog.destroy()

    def run(self):
        """Run the application"""
        try:
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
            self.root.mainloop()
        except KeyboardInterrupt:
            self.on_closing()
    
    def on_closing(self):
        """Handle application closing"""
        try:
            # Stop auto-refresh
            if self.refresh_timer:
                self.root.after_cancel(self.refresh_timer)
            
            # Stop all sites
            self.site_manager.stop_all_sites()
            
            # Destroy window
            self.root.destroy()
        except:
            pass

    def update_status(self, message):
        """Update main status message"""
        self.status_label.config(text=f"🚀 {message}")

    def update_activity_indicator(self, activity):
        """Update activity indicator"""
        activity_icons = {
            'idle': '💤 Idle',
            'uploading': '📤 Uploading',
            'downloading': '📥 Downloading',
            'syncing': '🔄 Syncing',
            'connecting': '🔗 Connecting',
            'error': '❌ Error'
        }
        self.activity_indicator.config(text=activity_icons.get(activity, f"⚡ {activity.title()}"))

    def update_connection_status(self, connected=False, site_count=0):
        """Update connection status"""
        if connected and site_count > 0:
            self.connection_status.config(text=f"🟢 {site_count} Site{'s' if site_count != 1 else ''}")
        elif connected:
            self.connection_status.config(text="🟡 Connected")
        else:
            self.connection_status.config(text="🔴 Offline")
