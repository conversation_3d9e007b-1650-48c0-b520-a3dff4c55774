#!/usr/bin/env python3
"""
Modern transfer manager with real-time progress monitoring
"""

import tkinter as tk
from tkinter import ttk
import threading
import queue
import time
from datetime import datetime
from typing import Dict, List, Optional, Callable
from enum import Enum


class TransferStatus(Enum):
    PENDING = "pending"
    RUNNING = "running"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class TransferItem:
    """Represents a file transfer operation"""
    def __init__(self, source: str, destination: str, size: int = 0, 
                 operation: str = "upload", site_name: str = ""):
        self.id = f"{operation}_{int(time.time() * 1000)}"
        self.source = source
        self.destination = destination
        self.size = size
        self.operation = operation  # upload, download, sync
        self.site_name = site_name
        self.status = TransferStatus.PENDING
        self.progress = 0.0  # 0.0 to 1.0
        self.bytes_transferred = 0
        self.speed = 0  # bytes per second
        self.start_time = None
        self.end_time = None
        self.error_message = ""
        self.can_pause = True
        self.can_resume = True


class TransferQueue:
    """Manages transfer operations queue"""
    def __init__(self):
        self.items: Dict[str, TransferItem] = {}
        self.queue = queue.Queue()
        self.active_transfers = {}
        self.max_concurrent = 3
        self.callbacks = {}
    
    def add_transfer(self, transfer_item: TransferItem):
        """Add transfer to queue"""
        self.items[transfer_item.id] = transfer_item
        self.queue.put(transfer_item.id)
        self._notify_callback('item_added', transfer_item)
    
    def start_transfer(self, transfer_id: str):
        """Start a specific transfer"""
        if transfer_id in self.items:
            transfer_item = self.items[transfer_id]
            transfer_item.status = TransferStatus.RUNNING
            transfer_item.start_time = datetime.now()
            self._notify_callback('item_updated', transfer_item)
    
    def pause_transfer(self, transfer_id: str):
        """Pause a transfer"""
        if transfer_id in self.items:
            transfer_item = self.items[transfer_id]
            if transfer_item.can_pause:
                transfer_item.status = TransferStatus.PAUSED
                self._notify_callback('item_updated', transfer_item)
    
    def resume_transfer(self, transfer_id: str):
        """Resume a paused transfer"""
        if transfer_id in self.items:
            transfer_item = self.items[transfer_id]
            if transfer_item.can_resume:
                transfer_item.status = TransferStatus.RUNNING
                self._notify_callback('item_updated', transfer_item)
    
    def cancel_transfer(self, transfer_id: str):
        """Cancel a transfer"""
        if transfer_id in self.items:
            transfer_item = self.items[transfer_id]
            transfer_item.status = TransferStatus.CANCELLED
            transfer_item.end_time = datetime.now()
            self._notify_callback('item_updated', transfer_item)
    
    def update_progress(self, transfer_id: str, bytes_transferred: int, speed: int = 0):
        """Update transfer progress"""
        if transfer_id in self.items:
            transfer_item = self.items[transfer_id]
            transfer_item.bytes_transferred = bytes_transferred
            transfer_item.speed = speed
            if transfer_item.size > 0:
                transfer_item.progress = min(bytes_transferred / transfer_item.size, 1.0)
            self._notify_callback('progress_updated', transfer_item)
    
    def complete_transfer(self, transfer_id: str, success: bool = True, error: str = ""):
        """Mark transfer as completed"""
        if transfer_id in self.items:
            transfer_item = self.items[transfer_id]
            transfer_item.status = TransferStatus.COMPLETED if success else TransferStatus.FAILED
            transfer_item.end_time = datetime.now()
            transfer_item.error_message = error
            transfer_item.progress = 1.0 if success else transfer_item.progress
            self._notify_callback('item_updated', transfer_item)
    
    def _notify_callback(self, event: str, transfer_item: TransferItem):
        """Notify callbacks"""
        if event in self.callbacks:
            self.callbacks[event](transfer_item)
    
    def set_callback(self, event: str, callback: Callable):
        """Set callback for events"""
        self.callbacks[event] = callback


class TransferManagerWindow:
    """Modern transfer manager window"""
    
    def __init__(self, parent):
        self.parent = parent
        self.transfer_queue = TransferQueue()
        self.window = None
        self.tree = None
        self.item_map = {}  # tree_item_id -> transfer_id
        self.is_visible = False
        
        # Setup callbacks
        self.transfer_queue.set_callback('item_added', self.on_item_added)
        self.transfer_queue.set_callback('item_updated', self.on_item_updated)
        self.transfer_queue.set_callback('progress_updated', self.on_progress_updated)
    
    def show(self):
        """Show transfer manager window"""
        if self.window is None:
            self.create_window()
        
        self.window.deiconify()
        self.window.lift()
        self.is_visible = True
    
    def hide(self):
        """Hide transfer manager window"""
        if self.window:
            self.window.withdraw()
        self.is_visible = False
    
    def create_window(self):
        """Create transfer manager window"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("🚀 Transfer Manager")
        self.window.geometry("800x500")
        self.window.transient(self.parent)
        
        # Don't destroy on close, just hide
        self.window.protocol("WM_DELETE_WINDOW", self.hide)
        
        self.create_widgets()
        self.setup_bindings()
    
    def create_widgets(self):
        """Create window widgets"""
        # Main frame
        main_frame = ttk.Frame(self.window, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Header
        header_frame = ttk.Frame(main_frame)
        header_frame.pack(fill=tk.X, pady=(0, 10))
        
        title_label = ttk.Label(header_frame, text="🚀 Transfer Manager", 
                               font=('SF Pro Display', 16, 'bold'))
        title_label.pack(side=tk.LEFT)
        
        # Control buttons
        controls_frame = ttk.Frame(header_frame)
        controls_frame.pack(side=tk.RIGHT)
        
        ttk.Button(controls_frame, text="⏸️ Pause All", 
                  command=self.pause_all).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(controls_frame, text="▶️ Resume All", 
                  command=self.resume_all).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(controls_frame, text="🗑️ Clear Completed", 
                  command=self.clear_completed).pack(side=tk.LEFT, padx=(0, 5))
        
        # Transfer list
        list_frame = ttk.Frame(main_frame)
        list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # Create treeview
        columns = ('status', 'progress', 'speed', 'size', 'time')
        self.tree = ttk.Treeview(list_frame, columns=columns, show='tree headings', height=15)
        
        # Configure columns
        self.tree.heading('#0', text='📁 Transfer', anchor='w')
        self.tree.heading('status', text='📊 Status', anchor='center')
        self.tree.heading('progress', text='📈 Progress', anchor='center')
        self.tree.heading('speed', text='⚡ Speed', anchor='center')
        self.tree.heading('size', text='📏 Size', anchor='center')
        self.tree.heading('time', text='🕒 Time', anchor='center')
        
        # Column widths
        self.tree.column('#0', width=300, minwidth=200)
        self.tree.column('status', width=100, minwidth=80)
        self.tree.column('progress', width=120, minwidth=100)
        self.tree.column('speed', width=100, minwidth=80)
        self.tree.column('size', width=100, minwidth=80)
        self.tree.column('time', width=120, minwidth=100)
        
        # Scrollbar
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # Pack tree and scrollbar
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Status bar
        self.status_frame = ttk.Frame(main_frame)
        self.status_frame.pack(fill=tk.X)
        
        self.status_label = ttk.Label(self.status_frame, text="Ready")
        self.status_label.pack(side=tk.LEFT)
        
        # Overall progress
        self.overall_progress = ttk.Progressbar(self.status_frame, mode='determinate')
        self.overall_progress.pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=(10, 0))
        
        # Context menu
        self.create_context_menu()
    
    def create_context_menu(self):
        """Create context menu for transfers"""
        self.context_menu = tk.Menu(self.tree, tearoff=0)
        self.context_menu.add_command(label="▶️ Start", command=self.start_selected)
        self.context_menu.add_command(label="⏸️ Pause", command=self.pause_selected)
        self.context_menu.add_command(label="▶️ Resume", command=self.resume_selected)
        self.context_menu.add_command(label="❌ Cancel", command=self.cancel_selected)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="🔄 Retry", command=self.retry_selected)
        self.context_menu.add_command(label="🗑️ Remove", command=self.remove_selected)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="📋 Copy Path", command=self.copy_path)
        self.context_menu.add_command(label="📂 Show in Finder", command=self.show_in_finder)
    
    def setup_bindings(self):
        """Setup event bindings"""
        self.tree.bind('<Button-2>', self.show_context_menu)
        self.tree.bind('<Control-Button-1>', self.show_context_menu)
        self.tree.bind('<Double-1>', self.on_double_click)
    
    def show_context_menu(self, event):
        """Show context menu"""
        item = self.tree.identify_row(event.y)
        if item:
            self.tree.selection_set(item)
            self.context_menu.post(event.x_root, event.y_root)
    
    def on_double_click(self, event):
        """Handle double click - toggle pause/resume"""
        selection = self.tree.selection()
        if selection:
            item_id = selection[0]
            transfer_id = self.item_map.get(item_id)
            if transfer_id:
                transfer_item = self.transfer_queue.items.get(transfer_id)
                if transfer_item:
                    if transfer_item.status == TransferStatus.RUNNING:
                        self.transfer_queue.pause_transfer(transfer_id)
                    elif transfer_item.status == TransferStatus.PAUSED:
                        self.transfer_queue.resume_transfer(transfer_id)
    
    def format_size(self, size: int) -> str:
        """Format file size"""
        if size < 1024:
            return f"{size} B"
        elif size < 1024 * 1024:
            return f"{size / 1024:.1f} KB"
        elif size < 1024 * 1024 * 1024:
            return f"{size / (1024 * 1024):.1f} MB"
        else:
            return f"{size / (1024 * 1024 * 1024):.1f} GB"
    
    def format_speed(self, speed: int) -> str:
        """Format transfer speed"""
        return f"{self.format_size(speed)}/s"
    
    def format_time(self, seconds: int) -> str:
        """Format time duration"""
        if seconds < 60:
            return f"{seconds}s"
        elif seconds < 3600:
            return f"{seconds // 60}m {seconds % 60}s"
        else:
            hours = seconds // 3600
            minutes = (seconds % 3600) // 60
            return f"{hours}h {minutes}m"
    
    def get_status_icon(self, status: TransferStatus) -> str:
        """Get icon for transfer status"""
        icons = {
            TransferStatus.PENDING: "⏳",
            TransferStatus.RUNNING: "🔄",
            TransferStatus.PAUSED: "⏸️",
            TransferStatus.COMPLETED: "✅",
            TransferStatus.FAILED: "❌",
            TransferStatus.CANCELLED: "🚫"
        }
        return icons.get(status, "❓")
    
    def on_item_added(self, transfer_item: TransferItem):
        """Handle new transfer item"""
        if not self.tree:
            return
        
        # Add to tree
        operation_icon = "📤" if transfer_item.operation == "upload" else "📥"
        text = f"{operation_icon} {transfer_item.source} → {transfer_item.destination}"
        
        item_id = self.tree.insert('', 'end', text=text,
                                  values=("⏳ Pending", "0%", "", 
                                         self.format_size(transfer_item.size), ""))
        
        self.item_map[item_id] = transfer_item.id
        self.update_status()
    
    def on_item_updated(self, transfer_item: TransferItem):
        """Handle transfer item update"""
        self.update_transfer_display(transfer_item)
        self.update_status()
    
    def on_progress_updated(self, transfer_item: TransferItem):
        """Handle progress update"""
        self.update_transfer_display(transfer_item)
    
    def update_transfer_display(self, transfer_item: TransferItem):
        """Update transfer display in tree"""
        if not self.tree:
            return
        
        # Find tree item
        tree_item_id = None
        for item_id, transfer_id in self.item_map.items():
            if transfer_id == transfer_item.id:
                tree_item_id = item_id
                break
        
        if not tree_item_id:
            return
        
        # Update values
        status_text = f"{self.get_status_icon(transfer_item.status)} {transfer_item.status.value.title()}"
        progress_text = f"{transfer_item.progress * 100:.1f}%"
        speed_text = self.format_speed(transfer_item.speed) if transfer_item.speed > 0 else ""
        
        # Calculate time
        time_text = ""
        if transfer_item.start_time:
            if transfer_item.end_time:
                duration = (transfer_item.end_time - transfer_item.start_time).total_seconds()
                time_text = self.format_time(int(duration))
            elif transfer_item.status == TransferStatus.RUNNING and transfer_item.speed > 0:
                remaining_bytes = transfer_item.size - transfer_item.bytes_transferred
                eta_seconds = remaining_bytes / transfer_item.speed if transfer_item.speed > 0 else 0
                time_text = f"ETA: {self.format_time(int(eta_seconds))}"
        
        self.tree.set(tree_item_id, 'status', status_text)
        self.tree.set(tree_item_id, 'progress', progress_text)
        self.tree.set(tree_item_id, 'speed', speed_text)
        self.tree.set(tree_item_id, 'time', time_text)
    
    def update_status(self):
        """Update overall status"""
        if not self.tree:
            return
        
        total_items = len(self.transfer_queue.items)
        completed_items = sum(1 for item in self.transfer_queue.items.values() 
                             if item.status in [TransferStatus.COMPLETED, TransferStatus.FAILED, TransferStatus.CANCELLED])
        running_items = sum(1 for item in self.transfer_queue.items.values() 
                           if item.status == TransferStatus.RUNNING)
        
        if total_items == 0:
            self.status_label.config(text="No transfers")
            self.overall_progress['value'] = 0
        else:
            self.status_label.config(text=f"{completed_items}/{total_items} completed, {running_items} running")
            self.overall_progress['value'] = (completed_items / total_items) * 100
    
    # Context menu actions
    def start_selected(self): pass
    def pause_selected(self): pass
    def resume_selected(self): pass
    def cancel_selected(self): pass
    def retry_selected(self): pass
    def remove_selected(self): pass
    def copy_path(self): pass
    def show_in_finder(self): pass
    
    def pause_all(self): pass
    def resume_all(self): pass
    def clear_completed(self): pass
