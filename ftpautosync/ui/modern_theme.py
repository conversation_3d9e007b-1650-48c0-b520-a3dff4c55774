#!/usr/bin/env python3
"""
Modern theme and styling for FTP AutoSync UI
"""

import tkinter as tk
from tkinter import ttk
import platform
import subprocess
import os


class ModernTheme:
    """Modern theme manager for FTP AutoSync"""

    def __init__(self, appearance_mode='auto'):
        self.is_macos = platform.system() == "Darwin"
        self.appearance_mode = appearance_mode  # 'auto', 'light', 'dark'
        self.system_appearance = self.detect_system_appearance()
        self.effective_appearance = self.get_effective_appearance()
        self.colors = self.get_color_scheme()
        self.fonts = self.get_font_scheme()

    def detect_system_appearance(self):
        """Detect system appearance (light/dark mode)"""
        if self.is_macos:
            try:
                # Use AppleScript to detect macOS appearance
                result = subprocess.run([
                    'osascript', '-e',
                    'tell application "System Events" to tell appearance preferences to get dark mode'
                ], capture_output=True, text=True, timeout=5)

                if result.returncode == 0:
                    return 'dark' if result.stdout.strip() == 'true' else 'light'
            except:
                pass

        # Fallback: try to detect from environment or default to light
        return os.environ.get('APPEARANCE_MODE', 'light')

    def get_effective_appearance(self):
        """Get the effective appearance based on user preference"""
        if self.appearance_mode == 'auto':
            return self.system_appearance
        else:
            return self.appearance_mode

    def set_appearance_mode(self, mode):
        """Set appearance mode and update colors"""
        self.appearance_mode = mode
        self.effective_appearance = self.get_effective_appearance()
        self.colors = self.get_color_scheme()
        return self.colors
    
    def get_color_scheme(self):
        """Get modern color scheme based on appearance"""
        is_dark = self.effective_appearance == 'dark'

        if self.is_macos:
            if is_dark:
                # macOS Dark Mode colors
                return {
                    'primary': '#0A84FF',      # Brighter blue for dark mode
                    'secondary': '#5E5CE6',    # Purple
                    'success': '#30D158',      # Green
                    'warning': '#FF9F0A',      # Orange
                    'danger': '#FF453A',       # Red
                    'background': '#1C1C1E',   # Dark gray
                    'surface': '#2C2C2E',      # Darker surface
                    'text': '#FFFFFF',         # White text
                    'text_secondary': '#8E8E93', # Gray
                    'border': '#38383A',       # Dark border
                    'accent': '#0A84FF',       # Accent blue
                    'hover': '#1C1C1E',        # Dark hover
                    'selected': '#0A84FF40',   # Semi-transparent blue
                    'button_text': '#FFFFFF',  # Explicit button text
                    'button_bg': '#007AFF',    # Button background
                }
            else:
                # macOS Light Mode colors
                return {
                    'primary': '#007AFF',      # Blue
                    'secondary': '#5856D6',    # Purple
                    'success': '#34C759',      # Green
                    'warning': '#FF9500',      # Orange
                    'danger': '#FF3B30',       # Red
                    'background': '#F2F2F7',   # Light gray
                    'surface': '#FFFFFF',      # White
                    'text': '#000000',         # Black
                    'text_secondary': '#8E8E93', # Gray
                    'border': '#C6C6C8',       # Light border
                    'accent': '#007AFF',       # Accent blue
                    'hover': '#E5F3FF',        # Light blue hover
                    'selected': '#007AFF20',   # Semi-transparent blue
                    'button_text': '#FFFFFF',  # Explicit button text
                    'button_bg': '#007AFF',    # Button background
                }
        else:
            if is_dark:
                # Windows/Linux Dark Mode colors
                return {
                    'primary': '#60CDFF',      # Light blue for dark mode
                    'secondary': '#8B8BF7',    # Purple
                    'success': '#6FCF97',      # Green
                    'warning': '#FFB946',      # Orange
                    'danger': '#F2994A',       # Red
                    'background': '#202020',   # Dark gray
                    'surface': '#2D2D30',      # Dark surface
                    'text': '#FFFFFF',         # White text
                    'text_secondary': '#CCCCCC', # Light gray
                    'border': '#3F3F46',       # Dark border
                    'accent': '#60CDFF',       # Accent blue
                    'hover': '#2D2D30',        # Dark hover
                    'selected': '#60CDFF40',   # Semi-transparent blue
                    'button_text': '#FFFFFF',  # Explicit button text
                    'button_bg': '#0078D4',    # Button background
                }
            else:
                # Windows/Linux Light Mode colors
                return {
                    'primary': '#0078D4',      # Microsoft blue
                    'secondary': '#6B69D6',    # Purple
                    'success': '#107C10',      # Green
                    'warning': '#FF8C00',      # Orange
                    'danger': '#D13438',       # Red
                    'background': '#F3F2F1',   # Light gray
                    'surface': '#FFFFFF',      # White
                    'text': '#323130',         # Dark gray
                    'text_secondary': '#605E5C', # Medium gray
                    'border': '#EDEBE9',       # Light border
                    'accent': '#0078D4',       # Accent blue
                    'hover': '#F3F2F1',        # Light hover
                    'selected': '#0078D420',   # Semi-transparent blue
                    'button_text': '#FFFFFF',  # Explicit button text
                    'button_bg': '#0078D4',    # Button background
                }
    
    def get_font_scheme(self):
        """Get modern font scheme"""
        if self.is_macos:
            return {
                'default': ('SF Pro Display', 13),
                'heading': ('SF Pro Display', 16, 'bold'),
                'title': ('SF Pro Display', 20, 'bold'),
                'small': ('SF Pro Display', 11),
                'mono': ('SF Mono', 11),
                'button': ('SF Pro Display', 13, 'medium'),
            }
        else:
            return {
                'default': ('Segoe UI', 10),
                'heading': ('Segoe UI', 14, 'bold'),
                'title': ('Segoe UI', 18, 'bold'),
                'small': ('Segoe UI', 9),
                'mono': ('Consolas', 10),
                'button': ('Segoe UI', 10),
            }
    
    def apply_theme(self, root):
        """Apply modern theme to root window"""
        style = ttk.Style(root)
        
        # Configure modern styles
        self.configure_button_styles(style)
        self.configure_frame_styles(style)
        self.configure_treeview_styles(style)
        self.configure_entry_styles(style)
        self.configure_label_styles(style)
        self.configure_progressbar_styles(style)
        
        # Set root window properties
        root.configure(bg=self.colors['background'])
        
        return style
    
    def configure_button_styles(self, style):
        """Configure modern button styles"""
        # Primary button (accent)
        style.configure('Accent.TButton',
                       background=self.colors['accent'],
                       foreground='white',
                       font=self.fonts['button'],
                       borderwidth=0,
                       focuscolor='none',
                       padding=(20, 10))
        
        style.map('Accent.TButton',
                 background=[('active', self.colors['primary']),
                           ('pressed', self.colors['secondary'])])
        
        # Success button
        style.configure('Success.TButton',
                       background=self.colors['success'],
                       foreground='white',
                       font=self.fonts['button'],
                       borderwidth=0,
                       focuscolor='none',
                       padding=(15, 8))
        
        # Warning button
        style.configure('Warning.TButton',
                       background=self.colors['warning'],
                       foreground='white',
                       font=self.fonts['button'],
                       borderwidth=0,
                       focuscolor='none',
                       padding=(15, 8))
        
        # Danger button
        style.configure('Danger.TButton',
                       background=self.colors['danger'],
                       foreground='white',
                       font=self.fonts['button'],
                       borderwidth=0,
                       focuscolor='none',
                       padding=(15, 8))
        
        # Default button improvements with explicit colors
        style.configure('TButton',
                       background=self.colors['button_bg'],
                       foreground=self.colors['button_text'],
                       font=self.fonts['button'],
                       borderwidth=1,
                       relief='solid',
                       focuscolor='none',
                       padding=(15, 8))

        # Button hover and pressed states
        style.map('TButton',
                 background=[('active', self.colors['primary']),
                           ('pressed', self.colors['secondary'])],
                 foreground=[('active', self.colors['button_text']),
                           ('pressed', self.colors['button_text'])],
                 relief=[('pressed', 'sunken'),
                        ('active', 'solid')])
    
    def configure_frame_styles(self, style):
        """Configure modern frame styles"""
        style.configure('TFrame',
                       background=self.colors['background'])
        
        style.configure('Card.TFrame',
                       background=self.colors['surface'],
                       relief='flat',
                       borderwidth=1)
        
        style.configure('TLabelframe',
                       background=self.colors['background'],
                       borderwidth=1,
                       relief='solid')
        
        style.configure('TLabelframe.Label',
                       background=self.colors['background'],
                       font=self.fonts['heading'])
    
    def configure_treeview_styles(self, style):
        """Configure modern treeview styles"""
        style.configure('Treeview',
                       background=self.colors['surface'],
                       foreground=self.colors['text'],
                       font=self.fonts['default'],
                       fieldbackground=self.colors['surface'],
                       borderwidth=1,
                       relief='solid')
        
        style.configure('Treeview.Heading',
                       background=self.colors['background'],
                       foreground=self.colors['text'],
                       font=self.fonts['heading'],
                       relief='flat',
                       borderwidth=1)
        
        style.map('Treeview',
                 background=[('selected', self.colors['selected'])],
                 foreground=[('selected', self.colors['text'])])
        
        style.map('Treeview.Heading',
                 background=[('active', self.colors['hover'])])
    
    def configure_entry_styles(self, style):
        """Configure modern entry styles"""
        style.configure('TEntry',
                       font=self.fonts['default'],
                       borderwidth=1,
                       relief='solid',
                       padding=(10, 8))
        
        style.configure('TCombobox',
                       font=self.fonts['default'],
                       borderwidth=1,
                       relief='solid',
                       padding=(10, 8))
    
    def configure_label_styles(self, style):
        """Configure modern label styles"""
        style.configure('TLabel',
                       background=self.colors['background'],
                       foreground=self.colors['text'],
                       font=self.fonts['default'])
        
        style.configure('Heading.TLabel',
                       background=self.colors['background'],
                       foreground=self.colors['text'],
                       font=self.fonts['heading'])
        
        style.configure('Title.TLabel',
                       background=self.colors['background'],
                       foreground=self.colors['text'],
                       font=self.fonts['title'])
        
        style.configure('Small.TLabel',
                       background=self.colors['background'],
                       foreground=self.colors['text_secondary'],
                       font=self.fonts['small'])
        
        style.configure('Success.TLabel',
                       background=self.colors['background'],
                       foreground=self.colors['success'],
                       font=self.fonts['default'])
        
        style.configure('Warning.TLabel',
                       background=self.colors['background'],
                       foreground=self.colors['warning'],
                       font=self.fonts['default'])
        
        style.configure('Danger.TLabel',
                       background=self.colors['background'],
                       foreground=self.colors['danger'],
                       font=self.fonts['default'])
    
    def configure_progressbar_styles(self, style):
        """Configure modern progressbar styles"""
        style.configure('TProgressbar',
                       background=self.colors['accent'],
                       troughcolor=self.colors['border'],
                       borderwidth=0,
                       lightcolor=self.colors['accent'],
                       darkcolor=self.colors['accent'])
        
        style.configure('Success.TProgressbar',
                       background=self.colors['success'],
                       troughcolor=self.colors['border'])
        
        style.configure('Warning.TProgressbar',
                       background=self.colors['warning'],
                       troughcolor=self.colors['border'])
        
        style.configure('Danger.TProgressbar',
                       background=self.colors['danger'],
                       troughcolor=self.colors['border'])
    
    def create_modern_separator(self, parent, orient='horizontal'):
        """Create a modern separator"""
        separator = ttk.Separator(parent, orient=orient)
        return separator
    
    def create_card_frame(self, parent, **kwargs):
        """Create a modern card-style frame"""
        frame = ttk.Frame(parent, style='Card.TFrame', **kwargs)
        return frame
    
    def create_icon_button(self, parent, text, command, icon="", style="TButton", **kwargs):
        """Create a modern icon button"""
        button_text = f"{icon} {text}" if icon else text
        button = ttk.Button(parent, text=button_text, command=command, style=style, **kwargs)
        return button


# Global theme instance
_theme_instance = None

def get_theme():
    """Get global theme instance"""
    global _theme_instance
    if _theme_instance is None:
        _theme_instance = ModernTheme()
    return _theme_instance

def set_theme_appearance(appearance_mode):
    """Set theme appearance mode"""
    global _theme_instance
    if _theme_instance is not None:
        _theme_instance.set_appearance_mode(appearance_mode)
    return get_theme()

def apply_modern_theme(root, appearance_mode='auto'):
    """Apply modern theme to root window"""
    global _theme_instance
    _theme_instance = ModernTheme(appearance_mode)
    return _theme_instance.apply_theme(root)

def refresh_theme(root):
    """Refresh theme on existing window"""
    theme = get_theme()
    return theme.apply_theme(root)


class AppearancePreferencesDialog:
    """Dialog for appearance preferences"""

    def __init__(self, parent, current_mode='auto'):
        self.parent = parent
        self.current_mode = current_mode
        self.result = None
        self.dialog = None

    def show(self):
        """Show appearance preferences dialog"""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title("🎨 Appearance Preferences")
        self.dialog.geometry("400x300")
        self.dialog.transient(self.parent)
        self.dialog.grab_set()

        # Center dialog
        self.dialog.update_idletasks()
        x = self.parent.winfo_x() + (self.parent.winfo_width() // 2) - (400 // 2)
        y = self.parent.winfo_y() + (self.parent.winfo_height() // 2) - (300 // 2)
        self.dialog.geometry(f"400x300+{x}+{y}")

        self.create_widgets()

        # Wait for dialog to close
        self.dialog.wait_window()
        return self.result

    def create_widgets(self):
        """Create dialog widgets"""
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Title
        title_label = ttk.Label(main_frame, text="🎨 Appearance Preferences",
                               style="Title.TLabel")
        title_label.pack(pady=(0, 20))

        # Description
        desc_label = ttk.Label(main_frame,
                              text="Choose how FTP AutoSync should appear:",
                              style="TLabel")
        desc_label.pack(pady=(0, 20))

        # Appearance options
        self.appearance_var = tk.StringVar(value=self.current_mode)

        options_frame = ttk.LabelFrame(main_frame, text="Appearance Mode", padding="15")
        options_frame.pack(fill=tk.X, pady=(0, 20))

        # Auto mode
        auto_radio = ttk.Radiobutton(options_frame,
                                    text="🔄 Auto (Follow System)",
                                    variable=self.appearance_var,
                                    value='auto')
        auto_radio.pack(anchor=tk.W, pady=5)

        auto_desc = ttk.Label(options_frame,
                             text="Automatically match your system's appearance",
                             style="Small.TLabel")
        auto_desc.pack(anchor=tk.W, padx=(20, 0), pady=(0, 10))

        # Light mode
        light_radio = ttk.Radiobutton(options_frame,
                                     text="☀️ Light Mode",
                                     variable=self.appearance_var,
                                     value='light')
        light_radio.pack(anchor=tk.W, pady=5)

        light_desc = ttk.Label(options_frame,
                              text="Always use light appearance",
                              style="Small.TLabel")
        light_desc.pack(anchor=tk.W, padx=(20, 0), pady=(0, 10))

        # Dark mode
        dark_radio = ttk.Radiobutton(options_frame,
                                    text="🌙 Dark Mode",
                                    variable=self.appearance_var,
                                    value='dark')
        dark_radio.pack(anchor=tk.W, pady=5)

        dark_desc = ttk.Label(options_frame,
                             text="Always use dark appearance",
                             style="Small.TLabel")
        dark_desc.pack(anchor=tk.W, padx=(20, 0), pady=(0, 10))

        # System info
        theme = get_theme()
        system_info = ttk.Label(main_frame,
                               text=f"System: {theme.system_appearance.title()} Mode Detected",
                               style="Small.TLabel")
        system_info.pack(pady=(0, 20))

        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)

        cancel_btn = ttk.Button(button_frame, text="Cancel", command=self.cancel)
        cancel_btn.pack(side=tk.RIGHT, padx=(5, 0))

        apply_btn = ttk.Button(button_frame, text="Apply", command=self.apply,
                              style="Accent.TButton")
        apply_btn.pack(side=tk.RIGHT)

    def apply(self):
        """Apply appearance changes"""
        self.result = self.appearance_var.get()
        self.dialog.destroy()

    def cancel(self):
        """Cancel changes"""
        self.result = None
        self.dialog.destroy()
