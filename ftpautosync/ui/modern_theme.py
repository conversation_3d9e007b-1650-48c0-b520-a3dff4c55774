#!/usr/bin/env python3
"""
Modern theme and styling for FTP AutoSync UI
"""

import tkinter as tk
from tkinter import ttk
import platform


class ModernTheme:
    """Modern theme manager for FTP AutoSync"""
    
    def __init__(self):
        self.is_macos = platform.system() == "Darwin"
        self.colors = self.get_color_scheme()
        self.fonts = self.get_font_scheme()
    
    def get_color_scheme(self):
        """Get modern color scheme"""
        if self.is_macos:
            # macOS-inspired colors
            return {
                'primary': '#007AFF',      # Blue
                'secondary': '#5856D6',    # Purple
                'success': '#34C759',      # Green
                'warning': '#FF9500',      # Orange
                'danger': '#FF3B30',       # Red
                'background': '#F2F2F7',   # Light gray
                'surface': '#FFFFFF',      # White
                'text': '#000000',         # Black
                'text_secondary': '#8E8E93', # Gray
                'border': '#C6C6C8',       # Light border
                'accent': '#007AFF',       # Accent blue
                'hover': '#E5F3FF',        # Light blue hover
                'selected': '#007AFF20',   # Semi-transparent blue
            }
        else:
            # Windows/Linux modern colors
            return {
                'primary': '#0078D4',      # Microsoft blue
                'secondary': '#6B69D6',    # Purple
                'success': '#107C10',      # Green
                'warning': '#FF8C00',      # Orange
                'danger': '#D13438',       # Red
                'background': '#F3F2F1',   # Light gray
                'surface': '#FFFFFF',      # White
                'text': '#323130',         # Dark gray
                'text_secondary': '#605E5C', # Medium gray
                'border': '#EDEBE9',       # Light border
                'accent': '#0078D4',       # Accent blue
                'hover': '#F3F2F1',        # Light hover
                'selected': '#0078D420',   # Semi-transparent blue
            }
    
    def get_font_scheme(self):
        """Get modern font scheme"""
        if self.is_macos:
            return {
                'default': ('SF Pro Display', 13),
                'heading': ('SF Pro Display', 16, 'bold'),
                'title': ('SF Pro Display', 20, 'bold'),
                'small': ('SF Pro Display', 11),
                'mono': ('SF Mono', 11),
                'button': ('SF Pro Display', 13, 'medium'),
            }
        else:
            return {
                'default': ('Segoe UI', 10),
                'heading': ('Segoe UI', 14, 'bold'),
                'title': ('Segoe UI', 18, 'bold'),
                'small': ('Segoe UI', 9),
                'mono': ('Consolas', 10),
                'button': ('Segoe UI', 10),
            }
    
    def apply_theme(self, root):
        """Apply modern theme to root window"""
        style = ttk.Style(root)
        
        # Configure modern styles
        self.configure_button_styles(style)
        self.configure_frame_styles(style)
        self.configure_treeview_styles(style)
        self.configure_entry_styles(style)
        self.configure_label_styles(style)
        self.configure_progressbar_styles(style)
        
        # Set root window properties
        root.configure(bg=self.colors['background'])
        
        return style
    
    def configure_button_styles(self, style):
        """Configure modern button styles"""
        # Primary button (accent)
        style.configure('Accent.TButton',
                       background=self.colors['accent'],
                       foreground='white',
                       font=self.fonts['button'],
                       borderwidth=0,
                       focuscolor='none',
                       padding=(20, 10))
        
        style.map('Accent.TButton',
                 background=[('active', self.colors['primary']),
                           ('pressed', self.colors['secondary'])])
        
        # Success button
        style.configure('Success.TButton',
                       background=self.colors['success'],
                       foreground='white',
                       font=self.fonts['button'],
                       borderwidth=0,
                       focuscolor='none',
                       padding=(15, 8))
        
        # Warning button
        style.configure('Warning.TButton',
                       background=self.colors['warning'],
                       foreground='white',
                       font=self.fonts['button'],
                       borderwidth=0,
                       focuscolor='none',
                       padding=(15, 8))
        
        # Danger button
        style.configure('Danger.TButton',
                       background=self.colors['danger'],
                       foreground='white',
                       font=self.fonts['button'],
                       borderwidth=0,
                       focuscolor='none',
                       padding=(15, 8))
        
        # Default button improvements
        style.configure('TButton',
                       font=self.fonts['button'],
                       padding=(15, 8))
    
    def configure_frame_styles(self, style):
        """Configure modern frame styles"""
        style.configure('TFrame',
                       background=self.colors['background'])
        
        style.configure('Card.TFrame',
                       background=self.colors['surface'],
                       relief='flat',
                       borderwidth=1)
        
        style.configure('TLabelframe',
                       background=self.colors['background'],
                       borderwidth=1,
                       relief='solid')
        
        style.configure('TLabelframe.Label',
                       background=self.colors['background'],
                       font=self.fonts['heading'])
    
    def configure_treeview_styles(self, style):
        """Configure modern treeview styles"""
        style.configure('Treeview',
                       background=self.colors['surface'],
                       foreground=self.colors['text'],
                       font=self.fonts['default'],
                       fieldbackground=self.colors['surface'],
                       borderwidth=1,
                       relief='solid')
        
        style.configure('Treeview.Heading',
                       background=self.colors['background'],
                       foreground=self.colors['text'],
                       font=self.fonts['heading'],
                       relief='flat',
                       borderwidth=1)
        
        style.map('Treeview',
                 background=[('selected', self.colors['selected'])],
                 foreground=[('selected', self.colors['text'])])
        
        style.map('Treeview.Heading',
                 background=[('active', self.colors['hover'])])
    
    def configure_entry_styles(self, style):
        """Configure modern entry styles"""
        style.configure('TEntry',
                       font=self.fonts['default'],
                       borderwidth=1,
                       relief='solid',
                       padding=(10, 8))
        
        style.configure('TCombobox',
                       font=self.fonts['default'],
                       borderwidth=1,
                       relief='solid',
                       padding=(10, 8))
    
    def configure_label_styles(self, style):
        """Configure modern label styles"""
        style.configure('TLabel',
                       background=self.colors['background'],
                       foreground=self.colors['text'],
                       font=self.fonts['default'])
        
        style.configure('Heading.TLabel',
                       background=self.colors['background'],
                       foreground=self.colors['text'],
                       font=self.fonts['heading'])
        
        style.configure('Title.TLabel',
                       background=self.colors['background'],
                       foreground=self.colors['text'],
                       font=self.fonts['title'])
        
        style.configure('Small.TLabel',
                       background=self.colors['background'],
                       foreground=self.colors['text_secondary'],
                       font=self.fonts['small'])
        
        style.configure('Success.TLabel',
                       background=self.colors['background'],
                       foreground=self.colors['success'],
                       font=self.fonts['default'])
        
        style.configure('Warning.TLabel',
                       background=self.colors['background'],
                       foreground=self.colors['warning'],
                       font=self.fonts['default'])
        
        style.configure('Danger.TLabel',
                       background=self.colors['background'],
                       foreground=self.colors['danger'],
                       font=self.fonts['default'])
    
    def configure_progressbar_styles(self, style):
        """Configure modern progressbar styles"""
        style.configure('TProgressbar',
                       background=self.colors['accent'],
                       troughcolor=self.colors['border'],
                       borderwidth=0,
                       lightcolor=self.colors['accent'],
                       darkcolor=self.colors['accent'])
        
        style.configure('Success.TProgressbar',
                       background=self.colors['success'],
                       troughcolor=self.colors['border'])
        
        style.configure('Warning.TProgressbar',
                       background=self.colors['warning'],
                       troughcolor=self.colors['border'])
        
        style.configure('Danger.TProgressbar',
                       background=self.colors['danger'],
                       troughcolor=self.colors['border'])
    
    def create_modern_separator(self, parent, orient='horizontal'):
        """Create a modern separator"""
        separator = ttk.Separator(parent, orient=orient)
        return separator
    
    def create_card_frame(self, parent, **kwargs):
        """Create a modern card-style frame"""
        frame = ttk.Frame(parent, style='Card.TFrame', **kwargs)
        return frame
    
    def create_icon_button(self, parent, text, command, icon="", style="TButton", **kwargs):
        """Create a modern icon button"""
        button_text = f"{icon} {text}" if icon else text
        button = ttk.Button(parent, text=button_text, command=command, style=style, **kwargs)
        return button


# Global theme instance
_theme_instance = None

def get_theme():
    """Get global theme instance"""
    global _theme_instance
    if _theme_instance is None:
        _theme_instance = ModernTheme()
    return _theme_instance

def apply_modern_theme(root):
    """Apply modern theme to root window"""
    theme = get_theme()
    return theme.apply_theme(root)
