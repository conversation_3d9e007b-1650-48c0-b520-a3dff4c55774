"""
Logging configuration for FTP AutoSync
"""

import logging
import logging.handlers
import colorlog
from pathlib import Path


class Logger:
    """Centralized logging configuration"""
    
    def __init__(self, config):
        self.config = config
        self.logger = None
        self._setup_logger()
    
    def _setup_logger(self):
        """Setup logger with file and console handlers"""
        self.logger = logging.getLogger('ftpautosync')
        self.logger.setLevel(getattr(logging, self.config['logging']['level']))
        
        # Clear any existing handlers
        self.logger.handlers.clear()
        
        # Console handler with colors
        console_handler = colorlog.StreamHandler()
        console_formatter = colorlog.ColoredFormatter(
            '%(log_color)s%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S',
            log_colors={
                'DEBUG': 'cyan',
                'INFO': 'green',
                'WARNING': 'yellow',
                'ERROR': 'red',
                'CRITICAL': 'red,bg_white',
            }
        )
        console_handler.setFormatter(console_formatter)
        self.logger.addHandler(console_handler)
        
        # File handler if specified
        log_file = self.config['logging'].get('file')
        if log_file:
            log_path = Path(log_file)
            log_path.parent.mkdir(parents=True, exist_ok=True)
            
            file_handler = logging.handlers.RotatingFileHandler(
                log_file,
                maxBytes=self.config['logging'].get('max_file_size', 10) * 1024 * 1024,
                backupCount=self.config['logging'].get('backup_count', 5)
            )
            file_formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
            file_handler.setFormatter(file_formatter)
            self.logger.addHandler(file_handler)
    
    def get_logger(self):
        """Get the configured logger instance"""
        return self.logger
