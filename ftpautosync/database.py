"""
Database management for multi-site FTP AutoSync
"""

import sqlite3
import json
import uuid
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any
import threading


class DatabaseManager:
    """Manages SQLite database for sites and logs"""
    
    def __init__(self, db_path: str = "ftpautosync.db"):
        self.db_path = Path(db_path)
        self.lock = threading.Lock()
        self.init_database()
    
    def init_database(self):
        """Initialize database tables"""
        with self.lock:
            conn = sqlite3.connect(self.db_path)
            try:
                # Sites table
                conn.execute('''
                    CREATE TABLE IF NOT EXISTS sites (
                        id TEXT PRIMARY KEY,
                        name TEXT NOT NULL UNIQUE,
                        config TEXT NOT NULL,
                        enabled BOOLEAN DEFAULT 1,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # Activity logs table
                conn.execute('''
                    CREATE TABLE IF NOT EXISTS activity_logs (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        site_id TEXT,
                        level TEXT NOT NULL,
                        message TEXT NOT NULL,
                        details TEXT,
                        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (site_id) REFERENCES sites (id)
                    )
                ''')
                
                # Site status table
                conn.execute('''
                    CREATE TABLE IF NOT EXISTS site_status (
                        site_id TEXT PRIMARY KEY,
                        status TEXT NOT NULL,
                        last_sync TIMESTAMP,
                        files_synced INTEGER DEFAULT 0,
                        errors_count INTEGER DEFAULT 0,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (site_id) REFERENCES sites (id)
                    )
                ''')
                
                conn.commit()
            finally:
                conn.close()
    
    def add_site(self, name: str, config: Dict[str, Any]) -> str:
        """Add a new site configuration"""
        site_id = str(uuid.uuid4())
        
        with self.lock:
            conn = sqlite3.connect(self.db_path)
            try:
                conn.execute(
                    'INSERT INTO sites (id, name, config) VALUES (?, ?, ?)',
                    (site_id, name, json.dumps(config))
                )
                
                # Initialize site status
                conn.execute(
                    'INSERT INTO site_status (site_id, status) VALUES (?, ?)',
                    (site_id, 'stopped')
                )
                
                conn.commit()
                return site_id
            finally:
                conn.close()
    
    def update_site(self, site_id: str, name: str = None, config: Dict[str, Any] = None, enabled: bool = None):
        """Update site configuration"""
        updates = []
        params = []
        
        if name is not None:
            updates.append('name = ?')
            params.append(name)
        
        if config is not None:
            updates.append('config = ?')
            params.append(json.dumps(config))
        
        if enabled is not None:
            updates.append('enabled = ?')
            params.append(enabled)
        
        if not updates:
            return
        
        updates.append('updated_at = CURRENT_TIMESTAMP')
        params.append(site_id)
        
        with self.lock:
            conn = sqlite3.connect(self.db_path)
            try:
                conn.execute(
                    f'UPDATE sites SET {", ".join(updates)} WHERE id = ?',
                    params
                )
                conn.commit()
            finally:
                conn.close()
    
    def delete_site(self, site_id: str):
        """Delete a site and all its data"""
        with self.lock:
            conn = sqlite3.connect(self.db_path)
            try:
                # Delete in order due to foreign key constraints
                conn.execute('DELETE FROM activity_logs WHERE site_id = ?', (site_id,))
                conn.execute('DELETE FROM site_status WHERE site_id = ?', (site_id,))
                conn.execute('DELETE FROM sites WHERE id = ?', (site_id,))
                conn.commit()
            finally:
                conn.close()
    
    def get_site(self, site_id: str) -> Optional[Dict[str, Any]]:
        """Get site by ID"""
        with self.lock:
            conn = sqlite3.connect(self.db_path)
            try:
                cursor = conn.execute(
                    'SELECT id, name, config, enabled, created_at, updated_at FROM sites WHERE id = ?',
                    (site_id,)
                )
                row = cursor.fetchone()
                
                if row:
                    return {
                        'id': row[0],
                        'name': row[1],
                        'config': json.loads(row[2]),
                        'enabled': bool(row[3]),
                        'created_at': row[4],
                        'updated_at': row[5]
                    }
                return None
            finally:
                conn.close()
    
    def get_all_sites(self) -> List[Dict[str, Any]]:
        """Get all sites"""
        with self.lock:
            conn = sqlite3.connect(self.db_path)
            try:
                cursor = conn.execute(
                    'SELECT id, name, config, enabled, created_at, updated_at FROM sites ORDER BY name'
                )
                
                sites = []
                for row in cursor.fetchall():
                    sites.append({
                        'id': row[0],
                        'name': row[1],
                        'config': json.loads(row[2]),
                        'enabled': bool(row[3]),
                        'created_at': row[4],
                        'updated_at': row[5]
                    })
                
                return sites
            finally:
                conn.close()
    
    def update_site_status(self, site_id: str, status: str, last_sync: datetime = None, 
                          files_synced: int = None, errors_count: int = None):
        """Update site status"""
        updates = ['status = ?', 'updated_at = CURRENT_TIMESTAMP']
        params = [status]
        
        if last_sync is not None:
            updates.append('last_sync = ?')
            params.append(last_sync.isoformat())
        
        if files_synced is not None:
            updates.append('files_synced = ?')
            params.append(files_synced)
        
        if errors_count is not None:
            updates.append('errors_count = ?')
            params.append(errors_count)
        
        params.append(site_id)
        
        with self.lock:
            conn = sqlite3.connect(self.db_path)
            try:
                conn.execute(
                    f'UPDATE site_status SET {", ".join(updates)} WHERE site_id = ?',
                    params
                )
                conn.commit()
            finally:
                conn.close()
    
    def get_site_status(self, site_id: str) -> Optional[Dict[str, Any]]:
        """Get site status"""
        with self.lock:
            conn = sqlite3.connect(self.db_path)
            try:
                cursor = conn.execute(
                    'SELECT status, last_sync, files_synced, errors_count, updated_at FROM site_status WHERE site_id = ?',
                    (site_id,)
                )
                row = cursor.fetchone()
                
                if row:
                    return {
                        'status': row[0],
                        'last_sync': row[1],
                        'files_synced': row[2] or 0,
                        'errors_count': row[3] or 0,
                        'updated_at': row[4]
                    }
                return None
            finally:
                conn.close()
    
    def add_activity_log(self, site_id: str, level: str, message: str, details: str = None):
        """Add activity log entry"""
        with self.lock:
            conn = sqlite3.connect(self.db_path)
            try:
                conn.execute(
                    'INSERT INTO activity_logs (site_id, level, message, details) VALUES (?, ?, ?, ?)',
                    (site_id, level, message, details)
                )
                conn.commit()
            finally:
                conn.close()
    
    def get_activity_logs(self, site_id: str = None, limit: int = 100) -> List[Dict[str, Any]]:
        """Get activity logs"""
        with self.lock:
            conn = sqlite3.connect(self.db_path)
            try:
                if site_id:
                    cursor = conn.execute(
                        '''SELECT al.id, al.site_id, s.name as site_name, al.level, al.message, 
                           al.details, al.timestamp 
                           FROM activity_logs al 
                           LEFT JOIN sites s ON al.site_id = s.id 
                           WHERE al.site_id = ? 
                           ORDER BY al.timestamp DESC LIMIT ?''',
                        (site_id, limit)
                    )
                else:
                    cursor = conn.execute(
                        '''SELECT al.id, al.site_id, s.name as site_name, al.level, al.message, 
                           al.details, al.timestamp 
                           FROM activity_logs al 
                           LEFT JOIN sites s ON al.site_id = s.id 
                           ORDER BY al.timestamp DESC LIMIT ?''',
                        (limit,)
                    )
                
                logs = []
                for row in cursor.fetchall():
                    logs.append({
                        'id': row[0],
                        'site_id': row[1],
                        'site_name': row[2] or 'Unknown',
                        'level': row[3],
                        'message': row[4],
                        'details': row[5],
                        'timestamp': row[6]
                    })
                
                return logs
            finally:
                conn.close()
    
    def clear_old_logs(self, days: int = 30):
        """Clear logs older than specified days"""
        with self.lock:
            conn = sqlite3.connect(self.db_path)
            try:
                conn.execute(
                    'DELETE FROM activity_logs WHERE timestamp < datetime("now", "-{} days")'.format(days)
                )
                conn.commit()
            finally:
                conn.close()
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get overall statistics"""
        with self.lock:
            conn = sqlite3.connect(self.db_path)
            try:
                # Total sites
                cursor = conn.execute('SELECT COUNT(*) FROM sites')
                total_sites = cursor.fetchone()[0]
                
                # Active sites
                cursor = conn.execute('SELECT COUNT(*) FROM site_status WHERE status = "running"')
                active_sites = cursor.fetchone()[0]
                
                # Total files synced
                cursor = conn.execute('SELECT SUM(files_synced) FROM site_status')
                total_files = cursor.fetchone()[0] or 0
                
                # Total errors
                cursor = conn.execute('SELECT SUM(errors_count) FROM site_status')
                total_errors = cursor.fetchone()[0] or 0
                
                # Recent activity count (last 24 hours)
                cursor = conn.execute(
                    'SELECT COUNT(*) FROM activity_logs WHERE timestamp > datetime("now", "-1 day")'
                )
                recent_activity = cursor.fetchone()[0]
                
                return {
                    'total_sites': total_sites,
                    'active_sites': active_sites,
                    'total_files_synced': total_files,
                    'total_errors': total_errors,
                    'recent_activity': recent_activity
                }
            finally:
                conn.close()
