"""
FTP client for file operations
"""

import ftplib
import os
import time
from pathlib import Path
from typing import Optional, List
import logging


class FTPClient:
    """FTP client with connection management and retry logic"""
    
    def __init__(self, config, logger):
        self.config = config
        self.logger = logger
        self.ftp = None
        self.connected = False
        
        # FTP settings
        self.host = config.get_ftp_config()['host']
        self.port = config.get_ftp_config().get('port', 21)
        self.username = config.get_ftp_config()['username']
        self.password = config.get_ftp_config()['password']
        self.passive = config.get_ftp_config().get('passive', True)
        self.timeout = config.get_ftp_config().get('timeout', 30)
        
        # Remote settings
        self.base_path = config.get_remote_config().get('base_path', '')
        self.create_dirs = config.get_remote_config().get('create_dirs', True)
        
        # Sync settings
        self.retry_attempts = config.get_sync_config().get('retry_attempts', 3)
        self.retry_delay = config.get_sync_config().get('retry_delay', 5)
    
    def connect(self) -> bool:
        """Connect to FTP server"""
        try:
            self.logger.info(f"Connecting to FTP server: {self.host}:{self.port}")
            
            self.ftp = ftplib.FTP()
            self.ftp.connect(self.host, self.port, self.timeout)
            self.ftp.login(self.username, self.password)
            
            if self.passive:
                self.ftp.set_pasv(True)
            
            # Change to base directory if specified
            if self.base_path:
                self.ftp.cwd(self.base_path)
            
            self.connected = True
            self.logger.info("Successfully connected to FTP server")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to connect to FTP server: {e}")
            self.connected = False
            return False
    
    def disconnect(self):
        """Disconnect from FTP server"""
        if self.ftp and self.connected:
            try:
                self.ftp.quit()
                self.logger.info("Disconnected from FTP server")
            except:
                try:
                    self.ftp.close()
                except:
                    pass
            finally:
                self.connected = False
                self.ftp = None
    
    def ensure_connection(self) -> bool:
        """Ensure FTP connection is active"""
        if not self.connected or not self.ftp:
            return self.connect()
        
        try:
            # Test connection with NOOP command
            self.ftp.voidcmd("NOOP")
            return True
        except:
            self.logger.warning("FTP connection lost, reconnecting...")
            self.connected = False
            return self.connect()
    
    def create_remote_directory(self, remote_path: str) -> bool:
        """Create remote directory if it doesn't exist"""
        if not self.create_dirs:
            return True
        
        try:
            # Try to change to the directory
            current_dir = self.ftp.pwd()
            try:
                self.ftp.cwd(remote_path)
                self.ftp.cwd(current_dir)  # Go back
                return True
            except ftplib.error_perm:
                # Directory doesn't exist, create it
                self.ftp.cwd(current_dir)  # Make sure we're in the right place
                
                # Create directories recursively
                parts = remote_path.strip('/').split('/')
                for i, part in enumerate(parts):
                    if not part:
                        continue
                    
                    partial_path = '/'.join(parts[:i+1])
                    try:
                        self.ftp.cwd(partial_path)
                    except ftplib.error_perm:
                        self.ftp.mkd(partial_path)
                        self.logger.info(f"Created remote directory: {partial_path}")
                
                self.ftp.cwd(current_dir)  # Go back to original directory
                return True
                
        except Exception as e:
            self.logger.error(f"Failed to create remote directory {remote_path}: {e}")
            return False
    
    def upload_file(self, local_path: Path, remote_path: str) -> bool:
        """Upload a file to FTP server with retry logic"""
        for attempt in range(self.retry_attempts):
            try:
                if not self.ensure_connection():
                    raise Exception("Cannot establish FTP connection")
                
                # Create remote directory if needed
                remote_dir = os.path.dirname(remote_path)
                if remote_dir and remote_dir != '.':
                    self.create_remote_directory(remote_dir)
                
                # Upload file
                with open(local_path, 'rb') as file:
                    self.ftp.storbinary(f'STOR {remote_path}', file)
                
                self.logger.info(f"Uploaded: {local_path} -> {remote_path}")
                return True
                
            except Exception as e:
                self.logger.warning(f"Upload attempt {attempt + 1} failed for {local_path}: {e}")
                
                if attempt < self.retry_attempts - 1:
                    self.logger.info(f"Retrying in {self.retry_delay} seconds...")
                    time.sleep(self.retry_delay)
                    self.connected = False  # Force reconnection
                else:
                    self.logger.error(f"Failed to upload {local_path} after {self.retry_attempts} attempts")
        
        return False
    
    def delete_file(self, remote_path: str) -> bool:
        """Delete a file from FTP server"""
        try:
            if not self.ensure_connection():
                raise Exception("Cannot establish FTP connection")
            
            self.ftp.delete(remote_path)
            self.logger.info(f"Deleted remote file: {remote_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to delete remote file {remote_path}: {e}")
            return False
    
    def list_files(self, remote_path: str = '.') -> List[str]:
        """List files in remote directory"""
        try:
            if not self.ensure_connection():
                return []
            
            return self.ftp.nlst(remote_path)
            
        except Exception as e:
            self.logger.error(f"Failed to list remote directory {remote_path}: {e}")
            return []
    
    def file_exists(self, remote_path: str) -> bool:
        """Check if file exists on remote server"""
        try:
            if not self.ensure_connection():
                return False
            
            # Try to get file size (will raise exception if file doesn't exist)
            self.ftp.size(remote_path)
            return True
            
        except:
            return False
