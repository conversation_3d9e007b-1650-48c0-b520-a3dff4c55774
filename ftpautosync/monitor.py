"""
File system monitoring using watchdog
"""

import fnmatch
from pathlib import Path
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
import logging


class FileChangeHandler(FileSystemEventHandler):
    """Handle file system events"""
    
    def __init__(self, sync_engine, config, logger):
        super().__init__()
        self.sync_engine = sync_engine
        self.config = config
        self.logger = logger
        self.exclude_patterns = config.get_exclude_patterns()
        self.watch_path = config.get_watch_path()
    
    def should_exclude(self, file_path: Path) -> bool:
        """Check if file should be excluded based on patterns"""
        relative_path = file_path.relative_to(self.watch_path)
        path_str = str(relative_path)
        
        for pattern in self.exclude_patterns:
            if fnmatch.fnmatch(path_str, pattern) or fnmatch.fnmatch(file_path.name, pattern):
                return True
        
        return False
    
    def on_modified(self, event):
        """Handle file modification events"""
        if event.is_directory:
            return
        
        file_path = Path(event.src_path)
        
        if self.should_exclude(file_path):
            self.logger.debug(f"Excluded from sync: {file_path}")
            return
        
        self.logger.info(f"File modified: {file_path}")
        self.sync_engine.queue_upload(file_path)
    
    def on_created(self, event):
        """Handle file creation events"""
        if event.is_directory:
            return
        
        file_path = Path(event.src_path)
        
        if self.should_exclude(file_path):
            self.logger.debug(f"Excluded from sync: {file_path}")
            return
        
        self.logger.info(f"File created: {file_path}")
        self.sync_engine.queue_upload(file_path)
    
    def on_deleted(self, event):
        """Handle file deletion events"""
        if event.is_directory:
            return
        
        file_path = Path(event.src_path)
        
        if self.should_exclude(file_path):
            return
        
        self.logger.info(f"File deleted: {file_path}")
        
        # Only delete remote file if configured to do so
        if self.config.get_sync_config().get('delete_remote', False):
            self.sync_engine.queue_delete(file_path)
    
    def on_moved(self, event):
        """Handle file move/rename events"""
        if event.is_directory:
            return
        
        old_path = Path(event.src_path)
        new_path = Path(event.dest_path)
        
        # Handle old file deletion
        if not self.should_exclude(old_path):
            self.logger.info(f"File moved from: {old_path}")
            if self.config.get_sync_config().get('delete_remote', False):
                self.sync_engine.queue_delete(old_path)
        
        # Handle new file creation
        if not self.should_exclude(new_path):
            self.logger.info(f"File moved to: {new_path}")
            self.sync_engine.queue_upload(new_path)


class FileMonitor:
    """File system monitor using watchdog"""
    
    def __init__(self, sync_engine, config, logger):
        self.sync_engine = sync_engine
        self.config = config
        self.logger = logger
        self.observer = None
        self.watch_path = config.get_watch_path()
        
        # Create watch directory if it doesn't exist
        self.watch_path.mkdir(parents=True, exist_ok=True)
    
    def start(self):
        """Start monitoring file changes"""
        if not self.watch_path.exists():
            raise FileNotFoundError(f"Watch path does not exist: {self.watch_path}")
        
        if not self.watch_path.is_dir():
            raise NotADirectoryError(f"Watch path is not a directory: {self.watch_path}")
        
        self.logger.info(f"Starting file monitor on: {self.watch_path}")
        
        event_handler = FileChangeHandler(self.sync_engine, self.config, self.logger)
        self.observer = Observer()
        self.observer.schedule(event_handler, str(self.watch_path), recursive=True)
        self.observer.start()
        
        self.logger.info("File monitor started successfully")
    
    def stop(self):
        """Stop monitoring file changes"""
        if self.observer and self.observer.is_alive():
            self.logger.info("Stopping file monitor...")
            self.observer.stop()
            self.observer.join()
            self.logger.info("File monitor stopped")
    
    def is_running(self) -> bool:
        """Check if monitor is running"""
        return self.observer and self.observer.is_alive()
