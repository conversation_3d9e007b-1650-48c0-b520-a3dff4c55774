# FTP AutoSync - Multi-Site Manager

A powerful Python application with native macOS UI that monitors file changes across multiple directories and automatically synchronizes them to different FTP servers in real-time.

## 🌟 Features

### Multi-Site Management
- **Multiple FTP sites**: Manage unlimited FTP sync sites from one interface
- **Individual controls**: Start/stop monitoring for each site independently
- **Site-specific configurations**: Each site has its own FTP settings, watch paths, and sync rules
- **Dynamic site management**: Add, edit, and delete sites without restarting

### Real-Time Monitoring & Sync
- **Real-time file monitoring**: Watches for file changes, creations, deletions, and moves
- **Automatic FTP sync**: Uploads modified files to FTP servers automatically
- **Intelligent retry logic**: Automatic retry for failed uploads with configurable attempts
- **Connection management**: Automatic FTP reconnection on connection loss
- **Exclude patterns**: Skip files/folders based on glob patterns

### Native macOS UI
- **Beautiful interface**: Native macOS-styled interface using Tkinter
- **Live dashboard**: Real-time status updates and statistics
- **Activity logging**: Blog-style activity feed with timestamps and details
- **Visual site management**: Easy-to-use forms for site configuration
- **Export capabilities**: Export activity logs and configurations

### Advanced Features
- **Database storage**: SQLite database for configurations and activity logs
- **Statistics tracking**: Monitor files synced, errors, and activity across all sites
- **Initial sync**: Option to sync existing files on startup
- **Comprehensive logging**: File and console logging with rotation
- **Configuration validation**: Built-in validation for all settings

## 🚀 Quick Start

### Option 1: Use the macOS UI (Recommended)

1. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Launch the application:**
   ```bash
   python main_ui.py
   # or use the launcher
   python launch_app.py
   ```

3. **Add your first site:**
   - Click "Add Site" in the UI
   - Fill in your FTP server details
   - Set the local directory to monitor
   - Click "Save" and then "Start"

### Option 2: Command Line (Original)

1. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Configure a site:**
   ```bash
   cp config.example.yaml config.yaml
   # Edit config.yaml with your settings
   ```

3. **Run:**
   ```bash
   python main.py
   ```

## 🖥️ macOS UI Guide

### Main Dashboard
The main window shows:
- **Statistics bar**: Total sites, active sites, files synced, and errors
- **Sites list**: All configured sites with their status and details
- **Activity log**: Real-time blog-style activity feed
- **Controls**: Start/stop individual sites or all sites at once

### Adding a Site
1. Click **"Add Site"** button
2. Fill in the **General** tab:
   - Site name (e.g., "My Website")
   - Description (optional)
3. Configure **FTP Server** tab:
   - Host, username, password
   - Port (usually 21)
   - Enable passive mode (recommended)
4. Set **Local Settings**:
   - Watch path (directory to monitor)
   - Exclude patterns (files to ignore)
5. Configure **Remote Settings**:
   - Base path on FTP server
   - Directory creation options
6. Adjust **Sync Settings**:
   - Retry attempts and delays
   - Initial sync options
7. Set **Logging** preferences
8. Click **"Test Connection"** to verify settings
9. Click **"Save"** to create the site

### Managing Sites
- **Start/Stop**: Use individual buttons or "Start All"/"Stop All"
- **Edit**: Double-click a site or select and click "Edit"
- **Delete**: Select a site and click "Delete"
- **Monitor**: Watch real-time status in the sites list

### Activity Log
- **Real-time updates**: See file uploads, errors, and status changes
- **Filter by site**: Use the dropdown to filter logs
- **Export logs**: Save activity history to a file
- **Clear logs**: Remove old log entries

## 📁 Project Structure

```
ftpautosync/
├── main_ui.py              # macOS UI application entry point
├── main.py                 # Command-line entry point
├── launch_app.py           # Simple launcher script
├── test_multisite.py       # Test suite for multi-site functionality
├── requirements.txt        # Python dependencies
├── config.yaml            # Single-site configuration (legacy)
├── config.example.yaml    # Configuration template
├── ftpautosync/           # Main package
│   ├── __init__.py
│   ├── database.py        # SQLite database management
│   ├── site_manager.py    # Multi-site management
│   ├── sync_engine.py     # File sync engine
│   ├── ftp_client.py      # FTP operations
│   ├── monitor.py         # File system monitoring
│   ├── config.py          # Configuration management
│   ├── logger.py          # Logging setup
│   └── ui/               # UI components
│       ├── __init__.py
│       ├── main_window.py        # Main application window
│       ├── site_config_dialog.py # Site configuration dialog
│       └── activity_viewer.py    # Activity log viewer
└── watch/                # Default watch directory
```

## Configuration (Legacy Single-Site)

Copy and edit the `config.yaml` file to match your setup:

```yaml
# FTP Server Settings
ftp:
  host: "your-ftp-server.com"
  port: 21
  username: "your_username"
  password: "your_password"
  passive: true
  timeout: 30

# Local directory to monitor
local:
  watch_path: "./watch"
  exclude_patterns:
    - "*.tmp"
    - "*.log"
    - ".git/*"
    - "__pycache__/*"

# Remote FTP settings
remote:
  base_path: "/public_html"
  create_dirs: true

# Sync behavior
sync:
  mode: "upload_only"
  initial_sync: false
  retry_attempts: 3
  retry_delay: 5
  delete_remote: false

# Logging
logging:
  level: "INFO"
  file: "ftpautosync.log"
```

### Configuration Options

#### FTP Settings
- `host`: FTP server hostname or IP address
- `port`: FTP server port (default: 21)
- `username`: FTP username
- `password`: FTP password
- `passive`: Use passive mode (recommended: true)
- `timeout`: Connection timeout in seconds

#### Local Settings
- `watch_path`: Local directory to monitor for changes
- `exclude_patterns`: List of glob patterns to exclude from sync

#### Remote Settings
- `base_path`: Base directory on FTP server (empty for root)
- `create_dirs`: Automatically create remote directories
- `file_permissions`: File permissions for uploaded files (optional)

#### Sync Settings
- `mode`: Sync mode ("upload_only" or "bidirectional")
- `initial_sync`: Sync existing files on startup
- `retry_attempts`: Number of retry attempts for failed uploads
- `retry_delay`: Delay between retries in seconds
- `delete_remote`: Delete remote files when local files are deleted

#### Logging Settings
- `level`: Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
- `file`: Log file path (empty for console only)
- `max_file_size`: Maximum log file size in MB
- `backup_count`: Number of backup log files to keep

## Usage

### Basic Usage

Start monitoring and syncing:
```bash
python main.py
```

Use a custom configuration file:
```bash
python main.py --config /path/to/config.yaml
```

### Test FTP Connection

Test your FTP connection without starting the sync:
```bash
python main.py --test-connection
```

### Check Status

View current status:
```bash
python main.py --status
```

### Command Line Options

- `--config, -c`: Path to configuration file (default: config.yaml)
- `--test-connection, -t`: Test FTP connection and exit
- `--status, -s`: Show current status and exit
- `--help, -h`: Show help message

## How It Works

1. **File Monitoring**: Uses the `watchdog` library to monitor file system events
2. **Event Processing**: Filters events based on exclude patterns
3. **Queue Management**: Queues file operations for processing
4. **FTP Operations**: Uploads/deletes files via FTP with retry logic
5. **Connection Management**: Maintains FTP connection with automatic reconnection

## File Operations

### Supported Events
- **File Created**: Automatically uploads new files
- **File Modified**: Re-uploads modified files
- **File Deleted**: Optionally deletes remote files (if `delete_remote: true`)
- **File Moved/Renamed**: Handles as delete + create operations

### Exclude Patterns
Use glob patterns to exclude files/directories:
- `*.tmp` - Exclude all .tmp files
- `.git/*` - Exclude entire .git directory
- `__pycache__/*` - Exclude Python cache directories
- `*.log` - Exclude log files

## Logging

The application provides comprehensive logging:
- **Console Output**: Colored log messages for easy reading
- **File Logging**: Rotating log files with configurable size limits
- **Log Levels**: Configurable verbosity (DEBUG, INFO, WARNING, ERROR, CRITICAL)

## Error Handling

- **Connection Errors**: Automatic reconnection with retry logic
- **Upload Failures**: Configurable retry attempts with delays
- **File System Errors**: Graceful handling of file access issues
- **Configuration Errors**: Clear error messages for invalid configurations

## Security Considerations

- Store FTP credentials securely
- Use strong passwords
- Consider using FTPS (FTP over SSL/TLS) if supported
- Limit FTP user permissions to necessary directories only
- Use firewall rules to restrict FTP access

## Troubleshooting

### Common Issues

1. **Connection Refused**
   - Check FTP server hostname and port
   - Verify firewall settings
   - Ensure FTP service is running

2. **Authentication Failed**
   - Verify username and password
   - Check if account is active and not locked

3. **Permission Denied**
   - Verify FTP user has write permissions
   - Check directory permissions on server

4. **Files Not Syncing**
   - Check exclude patterns
   - Verify watch path exists and is accessible
   - Check log files for error messages

### Debug Mode

Enable debug logging for detailed information:
```yaml
logging:
  level: "DEBUG"
```

## License

This project is open source. Feel free to modify and distribute according to your needs.

## Contributing

Contributions are welcome! Please feel free to submit issues and pull requests.
