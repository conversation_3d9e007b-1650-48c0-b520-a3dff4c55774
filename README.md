# FTP AutoSync - Professional Multi-Site File Synchronization Tool

**🚀 The Ultimate FTP Sync Solution for Web Developers, DevOps Teams, and Content Managers**

## 🎯 **Solve Your FTP Synchronization Challenges**

**Are you tired of manually uploading files to multiple FTP servers?** FTP AutoSync eliminates the pain of manual file management by providing **automated, real-time synchronization** across unlimited FTP sites with a **modern, Transmit-style interface**.

### **Problems This App Solves:**

🔥 **Manual FTP Upload Frustration** - Stop wasting time manually uploading files to multiple servers
🔥 **Multi-Site Management Chaos** - Manage unlimited FTP sites from one beautiful interface
🔥 **File Sync Inconsistencies** - Ensure all your sites stay perfectly synchronized automatically
🔥 **Deployment Workflow Bottlenecks** - Streamline your development-to-production pipeline
🔥 **Missing File Transfer Visibility** - Get real-time progress monitoring and detailed logs
🔥 **FTP Client Limitations** - Enjoy modern UI with dual-pane browsing and visual sync controls

### **Perfect For:**
- **🌐 Web Developers** - Sync local development to staging/production servers
- **⚙️ DevOps Teams** - Automate file deployment across multiple environments
- **📝 Content Managers** - Keep websites synchronized across multiple domains
- **🏢 Agencies** - Manage client sites efficiently from one application
- **🔧 System Administrators** - Maintain file consistency across server infrastructure

### **Technology Stack:**
- **🐍 Backend**: Python 3.8+ with asyncio for high-performance file operations
- **🖥️ Frontend**: Native macOS UI with Tkinter and modern styling
- **💾 Database**: SQLite for reliable configuration and activity storage
- **🔄 File Monitoring**: Watchdog library for real-time file system events
- **🌐 FTP Protocol**: Built-in ftplib with connection pooling and retry logic
- **📦 Packaging**: Virtual environment with automated dependency management

## 🌟 **Full-Stack Features**

### 🗂️ **Modern Transmit-Style File Manager**
- **Dual-Pane Interface** - Side-by-side local and remote file browsing
- **Right-Click Context Menus** - Upload, download, sync with visual feedback
- **File Type Icons** - Smart icons for documents 📄, images 🖼️, code 💻, archives 📦
- **Visual Sync Control** - Enable/disable sync for individual files and folders
- **Drag & Drop Support** - Intuitive file operations between panes
- **Multi-Selection** - Bulk operations with Ctrl/Cmd+click selection

### 🚀 **Real-Time Transfer Manager**
- **Live Progress Monitoring** - File-by-file progress with speed and ETA
- **Pause/Resume Controls** - Stop and restart transfers mid-operation
- **Transfer Queue** - See all pending, running, and completed operations
- **Bulk Operations** - Upload/download all files across all sites
- **Cancel Operations** - Stop transfers with confirmation dialogs
- **Retry Failed Transfers** - Automatic and manual retry mechanisms

### 🌐 **Multi-Site Management**
- **Unlimited FTP Sites** - SQLite database storage for scalable management
- **Individual Site Controls** - Start/stop monitoring per site independently
- **Site Import/Export** - YAML configuration with auto-loading and backup
- **Connection Monitoring** - Real-time status indicators and error recovery
- **Bulk Site Operations** - Manage multiple sites simultaneously

### 🔄 **Intelligent Synchronization**
- **Real-Time File Monitoring** - Instant detection using native file system events
- **Selective Sync** - Choose which files and folders to synchronize
- **Conflict Resolution** - Smart handling of file conflicts and overwrites
- **Exclusion Patterns** - Skip temporary files, caches, and unwanted directories
- **Retry Mechanisms** - Automatic retry on network failures with exponential backoff

### 🎨 **Professional Native UI**
- **Modern macOS Design** - SF Pro fonts, native styling, professional appearance
- **Keyboard Shortcuts** - ⌘F File Manager, ⌘T Transfer Manager, ⌘O Load Config
- **Status Indicators** - Real-time connection status and activity monitoring
- **Progress Dialogs** - Beautiful progress windows with detailed information
- **Menu Bar Integration** - Full menu system with recent files and preferences

### 📊 **Advanced Monitoring & Logging**
- **Activity Dashboard** - Real-time statistics and transfer progress
- **Detailed Audit Logs** - Complete history of all file operations
- **Export Capabilities** - Save logs and configurations for backup/sharing
- **Error Reporting** - Comprehensive error tracking with actionable insights
- **Performance Metrics** - Monitor transfer speeds, success rates, and system usage

## 🏆 **Why Choose FTP AutoSync?**

### **🆚 vs. Manual FTP Clients (FileZilla, WinSCP)**
✅ **Automated synchronization** vs. manual drag-and-drop
✅ **Multi-site management** vs. single connection management
✅ **Real-time monitoring** vs. manual refresh and checking
✅ **Visual sync control** vs. no sync state awareness
✅ **Bulk operations** vs. one-site-at-a-time operations

### **🆚 vs. Premium FTP Tools (Transmit, ForkLift)**
✅ **Open source and free** vs. expensive licensing
✅ **Unlimited sites** vs. license limitations
✅ **Automated workflows** vs. manual operations focus
✅ **Developer-friendly** vs. general consumer focus
✅ **Customizable and extensible** vs. closed-source limitations

### **🆚 vs. Command-Line Tools (rsync, lftp)**
✅ **Beautiful native UI** vs. command-line complexity
✅ **Visual progress monitoring** vs. text-only feedback
✅ **Point-and-click configuration** vs. complex scripting
✅ **Real-time file browsing** vs. blind file operations
✅ **Error handling with UI feedback** vs. cryptic error messages

### **🆚 vs. Cloud Sync Services (Dropbox, Google Drive)**
✅ **Direct FTP server control** vs. third-party dependency
✅ **No monthly fees** vs. subscription costs
✅ **Unlimited storage** vs. storage limits
✅ **Custom server support** vs. vendor lock-in
✅ **Privacy and security** vs. data stored on third-party servers

## 🚀 Quick Start

### Option 1: Complete Setup with Virtual Environment (Recommended)

1. **Run the setup script:**
   ```bash
   ./setup.sh
   ```
   This will:
   - Create a Python virtual environment
   - Install all dependencies
   - Test all modules
   - Check for your config.yaml

2. **Launch the UI:**
   ```bash
   make venv-ui
   ```

3. **Your config.yaml will be auto-loaded!**
   - The UI automatically loads `config.yaml` on startup
   - You'll see "📁 config.yaml loaded" in the header
   - Your existing configuration becomes a new site
   - Start monitoring immediately or add more sites

### Option 2: Manual Setup

1. **Create virtual environment:**
   ```bash
   python3 -m venv .venv
   source .venv/bin/activate
   ```

2. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Launch the application:**
   ```bash
   python main_ui.py
   ```

### Option 3: Command Line (Original)

1. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Configure a site:**
   ```bash
   cp config.example.yaml config.yaml
   # Edit config.yaml with your settings
   ```

3. **Run:**
   ```bash
   python main.py
   ```

### Option 4: Bulk Operations CLI

**Using Virtual Environment (Recommended):**
```bash
# Import your existing config.yaml
make venv-import

# List all sites
make venv-list

# Bulk upload all files
make venv-upload

# Bulk download all files
make venv-download
```

**Direct Commands:**
```bash
# Activate virtual environment first
source .venv/bin/activate

# Then use bulk operations
python bulk_operations.py import
python bulk_operations.py list
python bulk_operations.py upload
python bulk_operations.py download -o ./backups
python bulk_operations.py start
```

## 🖥️ macOS UI Guide

### Main Dashboard
The main window shows:
- **Statistics bar**: Total sites, active sites, files synced, and errors
- **Sites list**: All configured sites with their status and details
- **Activity log**: Real-time blog-style activity feed
- **Site controls**: Start/stop individual sites or all sites at once
- **Bulk operations**: Upload all, download all, sync all buttons
- **Import config**: Import existing config.yaml files

### Adding a Site
1. Click **"Add Site"** button
2. Fill in the **General** tab:
   - Site name (e.g., "My Website")
   - Description (optional)
3. Configure **FTP Server** tab:
   - Host, username, password
   - Port (usually 21)
   - Enable passive mode (recommended)
4. Set **Local Settings**:
   - Watch path (directory to monitor)
   - Exclude patterns (files to ignore)
5. Configure **Remote Settings**:
   - Base path on FTP server
   - Directory creation options
6. Adjust **Sync Settings**:
   - Retry attempts and delays
   - Initial sync options
7. Set **Logging** preferences
8. Click **"Test Connection"** to verify settings
9. Click **"Save"** to create the site

### Managing Sites
- **Start/Stop**: Use individual buttons or "Start All"/"Stop All"
- **Edit**: Double-click a site or select and click "Edit"
- **Delete**: Select a site and click "Delete"
- **Monitor**: Watch real-time status in the sites list

### Activity Log
- **Real-time updates**: See file uploads, errors, and status changes
- **Filter by site**: Use the dropdown to filter logs
- **Export logs**: Save activity history to a file
- **Clear logs**: Remove old log entries

### Bulk Operations
- **📤 Upload All**: Upload all files from all enabled sites to their FTP servers
- **📥 Download All**: Download all files from all enabled sites to local directories
- **🔄 Sync All**: Start monitoring all enabled sites simultaneously
- **Import Config**: Import existing config.yaml files as new sites

### Configuration File Loading

**Auto-Loading (Recommended):**
- Place your `config.yaml` in the project directory
- Launch the UI - it will automatically load and import your config
- Your existing configuration becomes a new site immediately

**Manual Loading:**
- **Menu**: File > Configuration > Load Config File... (⌘O)
- **Menu**: File > Configuration > Auto-Load config.yaml
- **Drag & Drop**: Drag YAML files directly onto the UI window
- **Recent Configs**: File > Configuration > Recent Configs

**Multiple Config Files:**
- The UI can load multiple config files
- Each config file becomes a separate site
- Supports `config.yaml`, `config.yml`, `ftpautosync.yaml`, etc.

**Export Sites:**
- File > Export All Sites... to save all sites to a YAML file
- Useful for backup or sharing configurations

## 📁 Project Structure

```
ftpautosync/
├── 🖥️  Applications
│   ├── main_ui.py              # macOS UI application
│   ├── main.py                 # Command-line interface (legacy)
│   └── bulk_operations.py      # Bulk operations CLI
│
├── 📦 Core Package
│   └── ftpautosync/
│       ├── __init__.py
│       ├── database.py         # SQLite database management
│       ├── site_manager.py     # Multi-site management
│       ├── sync_engine.py      # File sync engine
│       ├── ftp_client.py       # FTP operations
│       ├── monitor.py          # File system monitoring
│       ├── config.py           # Configuration management
│       ├── logger.py           # Logging setup
│       └── ui/                 # UI components
│           ├── __init__.py
│           ├── main_window.py          # Main application window
│           ├── site_config_dialog.py   # Site configuration dialog
│           └── activity_viewer.py      # Activity log viewer
│
├── 📋 Configuration & Documentation
│   ├── config.yaml             # Your existing configuration
│   ├── config.example.yaml     # Configuration template
│   ├── requirements.txt        # Python dependencies
│   ├── README.md              # This documentation
│   ├── Makefile               # Build commands
│   └── setup.sh               # Setup script with virtual environment
│
├── 🐍 Virtual Environment
│   └── .venv/                  # Python virtual environment (created by setup)
│
└── 🛠️  System Files
    └── ftpautosync.service     # Linux systemd service
```

## Configuration (Legacy Single-Site)

Copy and edit the `config.yaml` file to match your setup:

```yaml
# FTP Server Settings
ftp:
  host: "your-ftp-server.com"
  port: 21
  username: "your_username"
  password: "your_password"
  passive: true
  timeout: 30

# Local directory to monitor
local:
  watch_path: "./watch"
  exclude_patterns:
    - "*.tmp"
    - "*.log"
    - ".git/*"
    - "__pycache__/*"

# Remote FTP settings
remote:
  base_path: "/public_html"
  create_dirs: true

# Sync behavior
sync:
  mode: "upload_only"
  initial_sync: false
  retry_attempts: 3
  retry_delay: 5
  delete_remote: false

# Logging
logging:
  level: "INFO"
  file: "ftpautosync.log"
```

### Configuration Options

#### FTP Settings
- `host`: FTP server hostname or IP address
- `port`: FTP server port (default: 21)
- `username`: FTP username
- `password`: FTP password
- `passive`: Use passive mode (recommended: true)
- `timeout`: Connection timeout in seconds

#### Local Settings
- `watch_path`: Local directory to monitor for changes
- `exclude_patterns`: List of glob patterns to exclude from sync

#### Remote Settings
- `base_path`: Base directory on FTP server (empty for root)
- `create_dirs`: Automatically create remote directories
- `file_permissions`: File permissions for uploaded files (optional)

#### Sync Settings
- `mode`: Sync mode ("upload_only" or "bidirectional")
- `initial_sync`: Sync existing files on startup
- `retry_attempts`: Number of retry attempts for failed uploads
- `retry_delay`: Delay between retries in seconds
- `delete_remote`: Delete remote files when local files are deleted

#### Logging Settings
- `level`: Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
- `file`: Log file path (empty for console only)
- `max_file_size`: Maximum log file size in MB
- `backup_count`: Number of backup log files to keep

## Usage

### Basic Usage

Start monitoring and syncing:
```bash
python main.py
```

Use a custom configuration file:
```bash
python main.py --config /path/to/config.yaml
```

### Test FTP Connection

Test your FTP connection without starting the sync:
```bash
python main.py --test-connection
```

### Check Status

View current status:
```bash
python main.py --status
```

### Command Line Options

- `--config, -c`: Path to configuration file (default: config.yaml)
- `--test-connection, -t`: Test FTP connection and exit
- `--status, -s`: Show current status and exit
- `--help, -h`: Show help message

## How It Works

1. **File Monitoring**: Uses the `watchdog` library to monitor file system events
2. **Event Processing**: Filters events based on exclude patterns
3. **Queue Management**: Queues file operations for processing
4. **FTP Operations**: Uploads/deletes files via FTP with retry logic
5. **Connection Management**: Maintains FTP connection with automatic reconnection

## File Operations

### Supported Events
- **File Created**: Automatically uploads new files
- **File Modified**: Re-uploads modified files
- **File Deleted**: Optionally deletes remote files (if `delete_remote: true`)
- **File Moved/Renamed**: Handles as delete + create operations

### Exclude Patterns
Use glob patterns to exclude files/directories:
- `*.tmp` - Exclude all .tmp files
- `.git/*` - Exclude entire .git directory
- `__pycache__/*` - Exclude Python cache directories
- `*.log` - Exclude log files

## Logging

The application provides comprehensive logging:
- **Console Output**: Colored log messages for easy reading
- **File Logging**: Rotating log files with configurable size limits
- **Log Levels**: Configurable verbosity (DEBUG, INFO, WARNING, ERROR, CRITICAL)

## Error Handling

- **Connection Errors**: Automatic reconnection with retry logic
- **Upload Failures**: Configurable retry attempts with delays
- **File System Errors**: Graceful handling of file access issues
- **Configuration Errors**: Clear error messages for invalid configurations

## Security Considerations

- Store FTP credentials securely
- Use strong passwords
- Consider using FTPS (FTP over SSL/TLS) if supported
- Limit FTP user permissions to necessary directories only
- Use firewall rules to restrict FTP access

## Troubleshooting

### Common Issues

1. **Connection Refused**
   - Check FTP server hostname and port
   - Verify firewall settings
   - Ensure FTP service is running

2. **Authentication Failed**
   - Verify username and password
   - Check if account is active and not locked

3. **Permission Denied**
   - Verify FTP user has write permissions
   - Check directory permissions on server

4. **Files Not Syncing**
   - Check exclude patterns
   - Verify watch path exists and is accessible
   - Check log files for error messages

### Debug Mode

Enable debug logging for detailed information:
```yaml
logging:
  level: "DEBUG"
```

## License

This project is open source. Feel free to modify and distribute according to your needs.

## 🏷️ **Keywords & Tags**

**FTP Sync Tool** • **Multi-Site FTP Manager** • **Automated File Synchronization** • **Real-Time FTP Upload** • **macOS FTP Client** • **Python FTP Tool** • **Web Developer Tools** • **DevOps Automation** • **File Transfer Manager** • **Transmit Alternative** • **Open Source FTP** • **Bulk FTP Operations** • **Visual FTP Client** • **Site Deployment Tool** • **Content Management** • **Server Synchronization** • **Development Workflow** • **Production Deployment** • **File Monitoring** • **Cross-Platform FTP**

## 🎯 **Use Cases**

- **Web Development**: Sync local development files to staging and production servers
- **Content Management**: Keep multiple websites synchronized across different domains
- **DevOps Automation**: Automate file deployment in CI/CD pipelines
- **Agency Management**: Manage multiple client sites from one interface
- **Backup Solutions**: Create automated backups to FTP servers
- **Static Site Deployment**: Deploy Jekyll, Hugo, or other static sites
- **WordPress Management**: Sync themes, plugins, and content across sites
- **E-commerce Sites**: Keep product images and files synchronized
- **Documentation Sites**: Maintain documentation across multiple environments
- **Media Management**: Sync large media files to CDN servers

## 📈 **Performance & Scalability**

- **Concurrent Operations**: Handle multiple file transfers simultaneously
- **Memory Efficient**: Optimized for large file sets without memory bloat
- **Network Optimization**: Connection pooling and retry mechanisms
- **Cross-Platform**: Works on macOS, Linux, and Windows
- **Scalable Architecture**: Handle hundreds of sites and thousands of files
- **Low Resource Usage**: Minimal CPU and memory footprint
- **Fast File Detection**: Native file system events for instant change detection

## 🔧 **Integration & Automation**

- **CLI Interface**: Perfect for scripts and automation workflows
- **Configuration as Code**: YAML-based configuration for version control
- **Webhook Support**: Trigger syncs from external systems (planned)
- **API Integration**: REST API for external tool integration (planned)
- **Docker Support**: Containerized deployment options (planned)
- **CI/CD Integration**: Works with GitHub Actions, Jenkins, GitLab CI

## Contributing

Contributions are welcome! Please feel free to submit issues and pull requests.

**Development Areas:**
- 🐛 Bug fixes and stability improvements
- ✨ New features and enhancements
- 📚 Documentation improvements
- 🧪 Test coverage expansion
- 🎨 UI/UX improvements
- 🔧 Performance optimizations
