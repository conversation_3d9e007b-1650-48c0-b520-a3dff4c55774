#!/usr/bin/env python3
"""
FTP AutoSync - Main entry point
"""

import sys
import signal
import time
import argparse
from pathlib import Path

from ftpautosync.config import ConfigManager
from ftpautosync.logger import Logger
from ftpautosync.sync_engine import SyncEngine


def signal_handler(signum, frame):
    """Handle shutdown signals"""
    print("\nReceived shutdown signal. Stopping FTP AutoSync...")
    global sync_engine
    if sync_engine:
        sync_engine.stop()
    sys.exit(0)


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description='FTP AutoSync - Monitor and sync files via FTP')
    parser.add_argument(
        '--config', '-c',
        default='config.yaml',
        help='Path to configuration file (default: config.yaml)'
    )
    parser.add_argument(
        '--status', '-s',
        action='store_true',
        help='Show current status and exit'
    )
    parser.add_argument(
        '--test-connection', '-t',
        action='store_true',
        help='Test FTP connection and exit'
    )
    
    args = parser.parse_args()
    
    try:
        # Load configuration
        config = ConfigManager(args.config)
        
        # Setup logging
        logger_manager = Logger(config.config)
        logger = logger_manager.get_logger()
        
        logger.info("FTP AutoSync starting...")
        logger.info(f"Configuration loaded from: {args.config}")
        logger.info(f"Watch path: {config.get_watch_path()}")
        
        # Create sync engine
        global sync_engine
        sync_engine = SyncEngine(config, logger)
        
        # Handle command line options
        if args.test_connection:
            logger.info("Testing FTP connection...")
            if sync_engine.ftp_client.connect():
                logger.info("FTP connection test successful!")
                sync_engine.ftp_client.disconnect()
                return 0
            else:
                logger.error("FTP connection test failed!")
                return 1
        
        if args.status:
            status = sync_engine.get_status()
            print("FTP AutoSync Status:")
            for key, value in status.items():
                print(f"  {key}: {value}")
            return 0
        
        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        # Start sync engine
        sync_engine.start()
        
        logger.info("FTP AutoSync is running. Press Ctrl+C to stop.")
        
        # Keep the main thread alive
        try:
            while True:
                time.sleep(1)
                
                # Check if sync engine is still running
                if not sync_engine.running:
                    logger.error("Sync engine stopped unexpectedly")
                    break
                    
        except KeyboardInterrupt:
            logger.info("Received keyboard interrupt")
        
        # Cleanup
        sync_engine.stop()
        logger.info("FTP AutoSync stopped")
        return 0
        
    except FileNotFoundError as e:
        print(f"Error: {e}")
        print("Please create a configuration file or specify a valid path with --config")
        return 1
    except ValueError as e:
        print(f"Configuration error: {e}")
        return 1
    except Exception as e:
        print(f"Unexpected error: {e}")
        return 1


if __name__ == '__main__':
    sys.exit(main())
