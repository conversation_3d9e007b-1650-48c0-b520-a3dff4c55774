#!/usr/bin/env python3
"""
FTP AutoSync - macOS UI Application Entry Point
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox
import threading
import signal

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from ftpautosync.ui.main_window import MainWindow


def setup_macos_app():
    """Setup macOS specific application properties"""
    try:
        # Try to set macOS app properties
        import tkinter as tk
        root = tk.Tk()
        root.withdraw()  # Hide the root window
        
        # Set app name in menu bar
        try:
            root.tk.call('tk', 'appname', 'FTP AutoSync')
        except:
            pass
        
        # Try to set app icon if available
        try:
            # You can add an icon file here
            # root.iconbitmap('icon.icns')
            pass
        except:
            pass
        
        root.destroy()
        
    except Exception as e:
        print(f"Warning: Could not setup macOS app properties: {e}")


def handle_exception(exc_type, exc_value, exc_traceback):
    """Global exception handler"""
    if issubclass(exc_type, KeyboardInterrupt):
        sys.__excepthook__(exc_type, exc_value, exc_traceback)
        return
    
    error_msg = f"An unexpected error occurred:\n\n{exc_type.__name__}: {exc_value}"
    
    try:
        messagebox.showerror("Error", error_msg)
    except:
        print(error_msg)


def signal_handler(signum, frame):
    """Handle shutdown signals"""
    print("\nReceived shutdown signal. Closing application...")
    sys.exit(0)


def main():
    """Main entry point for the UI application"""
    try:
        # Setup macOS specific properties
        setup_macos_app()
        
        # Set global exception handler
        sys.excepthook = handle_exception
        
        # Setup signal handlers
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        # Check if we're running on macOS
        if sys.platform != 'darwin':
            print("Warning: This application is optimized for macOS but should work on other platforms.")
        
        # Create and run the main window
        app = MainWindow()
        
        print("Starting FTP AutoSync UI...")
        print("Close the application window or press Ctrl+C to exit.")
        
        app.run()
        
    except KeyboardInterrupt:
        print("\nApplication interrupted by user.")
        sys.exit(0)
    except Exception as e:
        error_msg = f"Failed to start application: {e}"
        print(error_msg)
        try:
            messagebox.showerror("Startup Error", error_msg)
        except:
            pass
        sys.exit(1)


if __name__ == '__main__':
    main()
