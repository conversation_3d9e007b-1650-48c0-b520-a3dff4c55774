#!/bin/bash
# FTP AutoSync Setup Script

set -e  # Exit on any error

echo "🚀 FTP AutoSync Setup"
echo "===================="

# Check Python version
echo "📋 Checking Python version..."
python3 --version || {
    echo "❌ Python 3 is required but not found"
    echo "Please install Python 3.8+ and try again"
    exit 1
}

# Create virtual environment if it doesn't exist
if [ ! -d ".venv" ]; then
    echo "🔧 Creating virtual environment..."
    python3 -m venv .venv
    echo "✅ Virtual environment created"
else
    echo "✅ Virtual environment already exists"
fi

# Activate virtual environment
echo "🔌 Activating virtual environment..."
source .venv/bin/activate

# Upgrade pip
echo "⬆️  Upgrading pip..."
pip install --upgrade pip

# Install requirements
echo "📦 Installing dependencies..."
pip install -r requirements.txt

# Test imports
echo "🧪 Testing core modules..."
python -c "from ftpautosync.site_manager import SiteManager; print('✅ Core modules working')" || {
    echo "❌ Core modules test failed"
    exit 1
}

echo "🧪 Testing UI modules..."
python -c "from ftpautosync.ui.main_window import MainWindow; print('✅ UI modules working')" || {
    echo "❌ UI modules test failed"
    exit 1
}

# Test CLI
echo "🧪 Testing CLI tools..."
python bulk_operations.py --help > /dev/null || {
    echo "❌ CLI tools test failed"
    exit 1
}

echo "✅ CLI tools working"

# Check for config.yaml
echo "📄 Checking for configuration files..."
if [ -f "config.yaml" ]; then
    echo "✅ Found config.yaml - will be auto-loaded by UI"
elif [ -f "config.yml" ]; then
    echo "✅ Found config.yml - will be auto-loaded by UI"
else
    echo "ℹ️  No config.yaml found - you can:"
    echo "   • Create one using config.example.yaml as template"
    echo "   • Import existing configs via the UI"
    echo "   • Add sites manually through the UI"
fi

echo ""
echo "🎉 Setup completed successfully!"
echo ""
echo "📋 Next steps:"
echo "   1. Activate virtual environment: source .venv/bin/activate"
echo "   2. Launch UI: python main_ui.py"
echo "   3. Or use CLI: python bulk_operations.py --help"
echo ""
echo "🔧 Available commands:"
echo "   make venv-ui          # Launch UI in virtual environment"
echo "   make venv-import      # Import config.yaml in virtual environment"
echo "   make venv-list        # List sites in virtual environment"
echo ""
echo "📚 Documentation: README.md"
echo ""
echo "Happy syncing! 🚀"
