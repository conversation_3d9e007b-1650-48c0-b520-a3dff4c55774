# FTP AutoSync Configuration

# FTP Server Settings
ftp:
  host: "www.abla.lat"
  port: 21
  username: "abla<PERSON>@abla.lat"
  password: "Brun2025#"
  # Use passive mode (recommended for most firewalls)
  passive: true
  # Connection timeout in seconds
  timeout: 30

# Local directory to monitor
local:
  # Path to watch for changes (can be relative or absolute)
  watch_path: "~/sites/abla.lat"
  # Patterns to exclude from sync (glob patterns)
  exclude_patterns:
    - "*.tmp"
    - "*.log"
    - ".git/*"
    - "__pycache__/*"
    - "*.pyc"
    - ".DS_Store"
    - "Thumbs.db"
    - "*.ini"
    - "*.exe"
    - "*.dll"

# Remote FTP settings
remote:
  # Base path on FTP server (leave empty for root)
  base_path: ""
  # Whether to create directories if they don't exist
  create_dirs: true
  # File permissions for uploaded files (octal, e.g., 644)
  file_permissions: null

# Sync behavior
sync:
  # Sync mode: "upload_only" or "bidirectional" (bidirectional not implemented yet)
  mode: "upload_only"
  # Whether to sync existing files on startup
  initial_sync: false
  # Retry attempts for failed uploads
  retry_attempts: 3
  # Delay between retries in seconds
  retry_delay: 5
  # Whether to delete remote files when local files are deleted
  delete_remote: false

# Logging configuration
logging:
  # Log level: DEBUG, INFO, WARNING, ERROR, CRITICAL
  level: "INFO"
  # Log file path (leave empty to log only to console)
  file: "ftpautosync.log"
  # Maximum log file size in MB
  max_file_size: 10
  # Number of backup log files to keep
  backup_count: 5
