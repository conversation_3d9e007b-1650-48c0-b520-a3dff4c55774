#!/usr/bin/env python3
"""
CLI tool for bulk FTP operations across all sites
"""

import sys
import os
import argparse
from pathlib import Path

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from ftpautosync.site_manager import SiteManager


def import_config(args):
    """Import legacy config file"""
    try:
        site_manager = SiteManager(args.database)
        
        config_file = args.config or "config.yaml"
        if not Path(config_file).exists():
            print(f"❌ Configuration file not found: {config_file}")
            return 1
        
        print(f"Importing configuration from {config_file}...")
        site_id = site_manager.import_legacy_config(config_file)
        
        site = site_manager.get_site(site_id)
        print(f"✅ Configuration imported successfully as '{site['name']}' (ID: {site_id})")
        
        return 0
        
    except Exception as e:
        print(f"❌ Failed to import configuration: {e}")
        return 1


def list_sites(args):
    """List all configured sites"""
    try:
        site_manager = SiteManager(args.database)
        sites = site_manager.get_all_sites()
        
        if not sites:
            print("No sites configured.")
            return 0
        
        print(f"Configured Sites ({len(sites)} total):")
        print("=" * 60)
        
        for site in sites:
            status_info = site_manager.get_site_status(site['id'])
            status = "🟢 Running" if status_info.get('running') else "⚪ Stopped"
            enabled = "✅ Enabled" if site['enabled'] else "❌ Disabled"
            
            print(f"Name: {site['name']}")
            print(f"  ID: {site['id']}")
            print(f"  Status: {status}")
            print(f"  Enabled: {enabled}")
            print(f"  FTP Host: {site['config']['ftp']['host']}")
            print(f"  Watch Path: {site['config']['local']['watch_path']}")
            print(f"  Files Synced: {status_info.get('files_synced', 0)}")
            print(f"  Errors: {status_info.get('errors_count', 0)}")
            print()
        
        return 0
        
    except Exception as e:
        print(f"❌ Failed to list sites: {e}")
        return 1


def bulk_upload(args):
    """Upload all files from all enabled sites"""
    try:
        site_manager = SiteManager(args.database)
        sites = site_manager.get_all_sites()
        enabled_sites = [s for s in sites if s['enabled']]
        
        if not enabled_sites:
            print("No enabled sites found.")
            return 0
        
        print(f"Starting bulk upload for {len(enabled_sites)} enabled sites...")
        print("=" * 60)
        
        results = site_manager.bulk_upload_all_sites()
        
        success_count = sum(1 for success in results.values() if success)
        total_count = len(results)
        
        print(f"\nBulk Upload Results:")
        print("=" * 30)
        print(f"Successful: {success_count}/{total_count}")
        print()
        
        for site_name, success in results.items():
            status = "✅ Success" if success else "❌ Failed"
            print(f"{status} - {site_name}")
        
        return 0 if success_count == total_count else 1
        
    except Exception as e:
        print(f"❌ Bulk upload failed: {e}")
        return 1


def bulk_download(args):
    """Download all files from all enabled sites"""
    try:
        site_manager = SiteManager(args.database)
        sites = site_manager.get_all_sites()
        enabled_sites = [s for s in sites if s['enabled']]
        
        if not enabled_sites:
            print("No enabled sites found.")
            return 0
        
        download_dir = args.output or "./downloads"
        print(f"Starting bulk download for {len(enabled_sites)} enabled sites to {download_dir}...")
        print("=" * 60)
        
        results = site_manager.bulk_download_all_sites(download_dir)
        
        success_count = sum(1 for success in results.values() if success)
        total_count = len(results)
        
        print(f"\nBulk Download Results:")
        print("=" * 30)
        print(f"Successful: {success_count}/{total_count}")
        print(f"Downloaded to: {download_dir}")
        print()
        
        for site_name, success in results.items():
            status = "✅ Success" if success else "❌ Failed"
            print(f"{status} - {site_name}")
        
        return 0 if success_count == total_count else 1
        
    except Exception as e:
        print(f"❌ Bulk download failed: {e}")
        return 1


def start_all(args):
    """Start monitoring all enabled sites"""
    try:
        site_manager = SiteManager(args.database)
        sites = site_manager.get_all_sites()
        enabled_sites = [s for s in sites if s['enabled']]
        
        if not enabled_sites:
            print("No enabled sites found.")
            return 0
        
        print(f"Starting monitoring for {len(enabled_sites)} enabled sites...")
        
        site_manager.start_all_enabled_sites()
        
        print("✅ All enabled sites started successfully.")
        print("Press Ctrl+C to stop monitoring.")
        
        # Keep running until interrupted
        try:
            import time
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\nStopping all sites...")
            site_manager.stop_all_sites()
            print("✅ All sites stopped.")
        
        return 0
        
    except Exception as e:
        print(f"❌ Failed to start sites: {e}")
        return 1


def stop_all(args):
    """Stop all running sites"""
    try:
        site_manager = SiteManager(args.database)
        
        print("Stopping all running sites...")
        site_manager.stop_all_sites()
        print("✅ All sites stopped.")
        
        return 0
        
    except Exception as e:
        print(f"❌ Failed to stop sites: {e}")
        return 1


def show_stats(args):
    """Show statistics"""
    try:
        site_manager = SiteManager(args.database)
        stats = site_manager.get_statistics()
        
        print("FTP AutoSync Statistics:")
        print("=" * 30)
        print(f"Total Sites: {stats['total_sites']}")
        print(f"Active Sites: {stats['active_sites']}")
        print(f"Files Synced: {stats['total_files_synced']}")
        print(f"Total Errors: {stats['total_errors']}")
        print(f"Recent Activity (24h): {stats['recent_activity']}")
        
        return 0
        
    except Exception as e:
        print(f"❌ Failed to get statistics: {e}")
        return 1


def show_logs(args):
    """Show recent activity logs"""
    try:
        site_manager = SiteManager(args.database)
        logs = site_manager.get_activity_logs(limit=args.limit or 20)
        
        if not logs:
            print("No activity logs found.")
            return 0
        
        print(f"Recent Activity Logs (last {len(logs)} entries):")
        print("=" * 60)
        
        for log in logs:
            timestamp = log['timestamp']
            level = log['level']
            site_name = log['site_name']
            message = log['message']
            
            print(f"[{timestamp}] {level} - {site_name}: {message}")
            if log['details']:
                print(f"  Details: {log['details']}")
        
        return 0

    except Exception as e:
        print(f"❌ Failed to get logs: {e}")
        return 1


def main():
    """Main CLI function"""
    parser = argparse.ArgumentParser(
        description='FTP AutoSync - Bulk Operations CLI',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s import                    # Import config.yaml
  %(prog)s import -c mysite.yaml     # Import specific config file
  %(prog)s list                      # List all sites
  %(prog)s upload                    # Upload all files from all sites
  %(prog)s download -o ./backups     # Download all files to ./backups
  %(prog)s start                     # Start monitoring all sites
  %(prog)s stop                      # Stop all sites
  %(prog)s stats                     # Show statistics
  %(prog)s logs -n 50                # Show last 50 log entries
        """
    )

    parser.add_argument(
        '--database', '-d',
        default='ftpautosync.db',
        help='Database file path (default: ftpautosync.db)'
    )

    subparsers = parser.add_subparsers(dest='command', help='Available commands')

    # Import command
    import_parser = subparsers.add_parser('import', help='Import legacy config file')
    import_parser.add_argument('--config', '-c', help='Config file to import (default: config.yaml)')
    import_parser.set_defaults(func=import_config)

    # List command
    list_parser = subparsers.add_parser('list', help='List all configured sites')
    list_parser.set_defaults(func=list_sites)

    # Upload command
    upload_parser = subparsers.add_parser('upload', help='Upload all files from all enabled sites')
    upload_parser.set_defaults(func=bulk_upload)

    # Download command
    download_parser = subparsers.add_parser('download', help='Download all files from all enabled sites')
    download_parser.add_argument('--output', '-o', help='Output directory (default: ./downloads)')
    download_parser.set_defaults(func=bulk_download)

    # Start command
    start_parser = subparsers.add_parser('start', help='Start monitoring all enabled sites')
    start_parser.set_defaults(func=start_all)

    # Stop command
    stop_parser = subparsers.add_parser('stop', help='Stop all running sites')
    stop_parser.set_defaults(func=stop_all)

    # Stats command
    stats_parser = subparsers.add_parser('stats', help='Show statistics')
    stats_parser.set_defaults(func=show_stats)

    # Logs command
    logs_parser = subparsers.add_parser('logs', help='Show recent activity logs')
    logs_parser.add_argument('--limit', '-n', type=int, help='Number of log entries to show (default: 20)')
    logs_parser.set_defaults(func=show_logs)

    args = parser.parse_args()

    if not args.command:
        parser.print_help()
        return 1

    try:
        return args.func(args)
    except KeyboardInterrupt:
        print("\nOperation cancelled by user.")
        return 1
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return 1


if __name__ == '__main__':
    sys.exit(main())
