#!/usr/bin/env python3
"""
Test script for multi-site FTP AutoSync functionality
"""

import sys
import os
from pathlib import Path

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from ftpautosync.database import DatabaseManager
from ftpautosync.site_manager import SiteManager


def test_database():
    """Test database functionality"""
    print("Testing database functionality...")
    
    # Create test database
    db = DatabaseManager("test.db")
    
    # Test site creation
    test_config = {
        'ftp': {
            'host': 'test.example.com',
            'port': 21,
            'username': 'testuser',
            'password': 'testpass',
            'passive': True,
            'timeout': 30
        },
        'local': {
            'watch_path': './test_watch',
            'exclude_patterns': ['*.tmp', '*.log']
        },
        'remote': {
            'base_path': '/public_html',
            'create_dirs': True
        },
        'sync': {
            'mode': 'upload_only',
            'initial_sync': False,
            'retry_attempts': 3,
            'retry_delay': 5,
            'delete_remote': False
        },
        'logging': {
            'level': 'INFO',
            'file': 'test.log',
            'max_file_size': 10,
            'backup_count': 5
        }
    }
    
    # Add test site
    site_id = db.add_site("Test Site", test_config)
    print(f"✓ Created test site with ID: {site_id}")
    
    # Test site retrieval
    site = db.get_site(site_id)
    print(f"✓ Retrieved site: {site['name']}")
    
    # Test activity logging
    db.add_activity_log(site_id, 'INFO', 'Test log message', 'Test details')
    print("✓ Added test activity log")
    
    # Test statistics
    stats = db.get_statistics()
    print(f"✓ Statistics: {stats}")
    
    # Cleanup
    db.delete_site(site_id)
    print("✓ Cleaned up test site")
    
    # Remove test database
    Path("test.db").unlink(missing_ok=True)
    print("✓ Database test completed successfully")


def test_site_manager():
    """Test site manager functionality"""
    print("\nTesting site manager functionality...")
    
    # Create test site manager
    site_manager = SiteManager("test_manager.db")
    
    # Test site creation
    test_config = {
        'ftp': {
            'host': 'test.example.com',
            'port': 21,
            'username': 'testuser',
            'password': 'testpass',
            'passive': True,
            'timeout': 30
        },
        'local': {
            'watch_path': './test_watch2',
            'exclude_patterns': ['*.tmp', '*.log']
        },
        'remote': {
            'base_path': '/public_html',
            'create_dirs': True
        },
        'sync': {
            'mode': 'upload_only',
            'initial_sync': False,
            'retry_attempts': 3,
            'retry_delay': 5,
            'delete_remote': False
        },
        'logging': {
            'level': 'INFO',
            'file': 'test.log',
            'max_file_size': 10,
            'backup_count': 5
        }
    }
    
    # Create test watch directory
    Path('./test_watch2').mkdir(exist_ok=True)
    
    try:
        # Add test site
        site_id = site_manager.create_site("Test Site Manager", test_config)
        print(f"✓ Created test site with ID: {site_id}")
        
        # Test site retrieval
        sites = site_manager.get_all_sites()
        print(f"✓ Retrieved {len(sites)} sites")
        
        # Test site status
        status = site_manager.get_site_status(site_id)
        print(f"✓ Site status: {status}")
        
        # Test statistics
        stats = site_manager.get_statistics()
        print(f"✓ Statistics: {stats}")
        
        # Cleanup
        site_manager.delete_site(site_id)
        print("✓ Cleaned up test site")
        
    finally:
        # Cleanup
        Path('./test_watch2').rmdir()
        Path("test_manager.db").unlink(missing_ok=True)
        print("✓ Site manager test completed successfully")


def main():
    """Main test function"""
    print("FTP AutoSync Multi-Site Test Suite")
    print("=" * 50)
    
    try:
        test_database()
        test_site_manager()
        
        print("\n🎉 All tests passed successfully!")
        print("\nYou can now run the UI with: python main_ui.py")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == '__main__':
    sys.exit(main())
