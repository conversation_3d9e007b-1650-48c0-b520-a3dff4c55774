Metadata-Version: 2.4
Name: ttkthemes
Version: 3.2.2
Summary: A group of themes for the ttk extensions of Tkinter with a Tkinter.Tk wrapper
Home-page: https://github.com/RedFantom/ttkthemes
Download-URL: https://github.com/RedFantom/ttkthemes/releases
Author: The ttkthemes authors
Author-email: <EMAIL>
License: GPLv3
Keywords: tkinter,ttk,gui,tcl,theme
Classifier: Programming Language :: Python :: 3.5
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Environment :: Win32 (MS Windows)
Classifier: Environment :: X11 Applications
Classifier: License :: OSI Approved :: GNU General Public License v3 (GPLv3)
Classifier: Topic :: Software Development :: Libraries :: Tcl Extensions
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Requires-Python: >=3.5
Description-Content-Type: text/markdown
Requires-Dist: pillow
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description
Dynamic: description-content-type
Dynamic: download-url
Dynamic: home-page
Dynamic: keywords
Dynamic: license
Dynamic: requires-dist
Dynamic: requires-python
Dynamic: summary

# ttkthemes 
[![Build status](https://ci.appveyor.com/api/projects/status/m7w8f06n5qs7486x/branch/master?svg=true)](https://ci.appveyor.com/project/RedFantom/ttkthemes/branch/master)
[![Build Status](https://travis-ci.org/TkinterEP/ttkthemes.svg?branch=master)](https://travis-ci.org/TkinterEP/ttkthemes)
[![codecov](https://codecov.io/gh/TkinterEP/ttkthemes/branch/master/graph/badge.svg?token=i6d7zapF75)](https://codecov.io/gh/TkinterEP/ttkthemes)
[![License: GPL v3](https://img.shields.io/badge/License-GPL%20v3-blue.svg)](http://www.gnu.org/licenses/gpl-3.0)
[![PyPI version](https://badge.fury.io/py/ttkthemes.svg)](https://pypi.python.org/pypi/ttkthemes)
[![Doc Status](https://readthedocs.org/projects/ttkthemes/badge/?version=latest&style=flat)](https://ttkthemes.readthedocs.io/en/latest)

A group of themes for the ttk extenstions for Tkinter gathered together by RedFantom and 
created by various authors.

## License
    ttkthemes: A group of themes for the ttk extensions of Tcl
    Copyright (C) 2017-2020 RedFantom
    Copyright (C) 2017-2018 Akuli
    Copyright (C) 2004 Pat Thoyts
    Copyright (C) 2004 David Zolli
    Copyright (C) 2007-2008 Mats Bengsston
    Copyright (C) 2005 Jelco Huijser
    Copyright (C) 2015-2018 The materia and equilux authors
    Copyright (C) 2018 Uwe Klimmek 
    Copyright (C) Regents of the University of California, Sun Microsystems, Inc., Scriptics Corporation, and other parties
    Copyright (c) 2018 Maximilian Lika
    Copyright (C) 2018-2020 The Yaru Theme Authors
    Copyright (C) 2015-2020 The Adapta Theme Authors
    
    This program is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.
    
    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.
    
    You should have received a copy of the GNU General Public License
    along with this program.  If not, see <http://www.gnu.org/licenses/>.``
    
Please note that while you can use some themes **only** under GPLv3 and that my code is also
available **only** under GPLv3, some themes, of which the files have a header explicitly stating
this, are also available under the BSD-like 2-clause Tcl license.

## Usage
For more information about how to use this project in your own programs, please check out the
[documentation](https://ttkthemes.readthedocs.io/en/latest/) on ReadTheDocs. You can also find
example images of the different included themes there, so you can choose one easily, as well
as examples on usage.
  
## Recommendations
The themes `plastik`, `clearlooks` and `elegance` are recommended to make your UI look nicer
on all platforms when using `Tkinter` and the `ttk` extensions in Python. When you are targeting
Ubuntu, consider using the great `radiance` theme.
