#!/usr/bin/env python3
"""
Setup script for FTP AutoSync
"""

import subprocess
import sys
import os
from pathlib import Path


def install_dependencies():
    """Install required dependencies"""
    print("Installing dependencies...")
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'])
        print("✓ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ Failed to install dependencies: {e}")
        return False


def create_watch_directory():
    """Create watch directory if it doesn't exist"""
    watch_dir = Path('watch')
    if not watch_dir.exists():
        watch_dir.mkdir(parents=True, exist_ok=True)
        print("✓ Created watch directory")
    else:
        print("✓ Watch directory already exists")


def check_config():
    """Check if config file exists and is valid"""
    config_file = Path('config.yaml')
    if not config_file.exists():
        print("⚠ Configuration file 'config.yaml' not found")
        print("  Please edit config.yaml with your FTP server details before running")
        return False
    
    print("✓ Configuration file found")
    return True


def main():
    """Main setup function"""
    print("FTP AutoSync Setup")
    print("=" * 50)
    
    # Install dependencies
    if not install_dependencies():
        return 1
    
    # Create watch directory
    create_watch_directory()
    
    # Check configuration
    config_exists = check_config()
    
    print("\nSetup completed!")
    print("\nNext steps:")
    if not config_exists:
        print("1. Edit config.yaml with your FTP server details")
        print("2. Test connection: python main.py --test-connection")
        print("3. Start syncing: python main.py")
    else:
        print("1. Test connection: python main.py --test-connection")
        print("2. Start syncing: python main.py")
    
    return 0


if __name__ == '__main__':
    sys.exit(main())
