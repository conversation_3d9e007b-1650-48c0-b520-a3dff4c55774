#!/usr/bin/env python3
"""
Create macOS app bundle for FTP AutoSync
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path


def create_app_bundle():
    """Create macOS .app bundle"""
    app_name = "FTP AutoSync"
    app_bundle = f"{app_name}.app"
    
    print(f"Creating {app_bundle}...")
    
    # Create app bundle structure
    bundle_path = Path(app_bundle)
    contents_path = bundle_path / "Contents"
    macos_path = contents_path / "MacOS"
    resources_path = contents_path / "Resources"
    
    # Remove existing bundle
    if bundle_path.exists():
        shutil.rmtree(bundle_path)
    
    # Create directories
    macos_path.mkdir(parents=True)
    resources_path.mkdir(parents=True)
    
    # Create Info.plist
    info_plist = f"""<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>CFBundleExecutable</key>
    <string>FTP AutoSync</string>
    <key>CFBundleIdentifier</key>
    <string>com.ftpautosync.app</string>
    <key>CFBundleName</key>
    <string>FTP AutoSync</string>
    <key>CFBundleVersion</key>
    <string>1.0.0</string>
    <key>CFBundleShortVersionString</key>
    <string>1.0.0</string>
    <key>CFBundleInfoDictionaryVersion</key>
    <string>6.0</string>
    <key>CFBundlePackageType</key>
    <string>APPL</string>
    <key>CFBundleSignature</key>
    <string>FTAS</string>
    <key>LSMinimumSystemVersion</key>
    <string>10.14</string>
    <key>NSHighResolutionCapable</key>
    <true/>
    <key>LSApplicationCategoryType</key>
    <string>public.app-category.developer-tools</string>
</dict>
</plist>"""
    
    with open(contents_path / "Info.plist", "w") as f:
        f.write(info_plist)
    
    # Create launcher script
    launcher_script = f"""#!/bin/bash
# FTP AutoSync Launcher Script

# Get the directory where this script is located
DIR="$( cd "$( dirname "${{BASH_SOURCE[0]}}" )" && pwd )"
RESOURCES_DIR="$DIR/../Resources"

# Change to resources directory
cd "$RESOURCES_DIR"

# Set Python path
export PYTHONPATH="$RESOURCES_DIR:$PYTHONPATH"

# Launch the application
python3 main_ui.py
"""
    
    launcher_path = macos_path / "FTP AutoSync"
    with open(launcher_path, "w") as f:
        f.write(launcher_script)
    
    # Make launcher executable
    os.chmod(launcher_path, 0o755)
    
    # Copy application files to Resources
    files_to_copy = [
        "main_ui.py",
        "main.py",
        "requirements.txt",
        "config.example.yaml",
        "ftpautosync/"
    ]
    
    for item in files_to_copy:
        src = Path(item)
        if src.exists():
            if src.is_dir():
                shutil.copytree(src, resources_path / src.name)
            else:
                shutil.copy2(src, resources_path)
    
    print(f"✓ Created {app_bundle}")
    print(f"✓ You can now run: open '{app_bundle}'")
    
    return bundle_path


def create_dmg():
    """Create DMG installer (requires create-dmg)"""
    try:
        # Check if create-dmg is available
        subprocess.run(["create-dmg", "--version"], check=True, capture_output=True)
        
        print("Creating DMG installer...")
        
        # Create DMG
        subprocess.run([
            "create-dmg",
            "--volname", "FTP AutoSync",
            "--window-pos", "200", "120",
            "--window-size", "600", "400",
            "--icon-size", "100",
            "--icon", "FTP AutoSync.app", "175", "120",
            "--hide-extension", "FTP AutoSync.app",
            "--app-drop-link", "425", "120",
            "FTP-AutoSync-Installer.dmg",
            "FTP AutoSync.app"
        ], check=True)
        
        print("✓ Created FTP-AutoSync-Installer.dmg")
        
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("⚠️  create-dmg not found. Install with: brew install create-dmg")
        print("   DMG creation skipped.")


def main():
    """Main function"""
    print("FTP AutoSync - macOS App Bundle Creator")
    print("=" * 40)
    
    # Check if we're on macOS
    if sys.platform != "darwin":
        print("❌ This script is designed for macOS only")
        return 1
    
    # Check if required files exist
    required_files = ["main_ui.py", "ftpautosync/"]
    for file in required_files:
        if not Path(file).exists():
            print(f"❌ Required file/directory not found: {file}")
            return 1
    
    try:
        # Create app bundle
        bundle_path = create_app_bundle()
        
        # Optionally create DMG
        create_dmg_choice = input("\nCreate DMG installer? (y/N): ").lower().strip()
        if create_dmg_choice in ['y', 'yes']:
            create_dmg()
        
        print("\n🎉 macOS app bundle created successfully!")
        print(f"\nTo run the app:")
        print(f"  open '{bundle_path}'")
        print(f"\nTo install:")
        print(f"  mv '{bundle_path}' /Applications/")
        
        return 0
        
    except Exception as e:
        print(f"❌ Failed to create app bundle: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
