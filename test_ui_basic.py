#!/usr/bin/env python3
"""
Basic UI test - just check if the UI can be imported and initialized
"""

import sys
import os

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test that all UI components can be imported"""
    print("Testing imports...")

    try:
        from ftpautosync.database import DatabaseManager
        print("✓ DatabaseManager imported")

        from ftpautosync.site_manager import SiteManager
        print("✓ SiteManager imported")

        from ftpautosync.ui.main_window import MainWindow
        print("✓ MainWindow imported")

        from ftpautosync.ui.site_config_dialog import SiteConfigDialog
        print("✓ SiteConfigDialog imported")

        from ftpautosync.ui.activity_viewer import ActivityViewer
        print("✓ ActivityViewer imported")

        return True

    except Exception as e:
        print(f"❌ Import failed: {e}")
        return False


def test_database():
    """Test basic database functionality"""
    print("\nTesting database...")

    try:
        from ftpautosync.database import DatabaseManager
        db = DatabaseManager("test_ui.db")
        print("✓ Database created")

        # Clean up
        import os
        os.unlink("test_ui.db")
        print("✓ Database test completed")

        return True

    except Exception as e:
        print(f"❌ Database test failed: {e}")
        return False


def test_tkinter():
    """Test that tkinter is available"""
    print("\nTesting tkinter...")

    try:
        import tkinter as tk
        from tkinter import ttk

        # Create a simple test window
        root = tk.Tk()
        root.withdraw()  # Hide it

        label = ttk.Label(root, text="Test")

        root.destroy()
        print("✓ Tkinter is working")

        return True

    except Exception as e:
        print(f"❌ Tkinter test failed: {e}")
        return False


def main():
    """Main test function"""
    print("FTP AutoSync UI - Basic Test")
    print("=" * 30)

    tests = [
        test_tkinter,
        test_imports,
        test_database
    ]

    passed = 0
    for test in tests:
        if test():
            passed += 1

    print(f"\n📊 Results: {passed}/{len(tests)} tests passed")

    if passed == len(tests):
        print("🎉 All tests passed! UI should work correctly.")
        print("\nYou can now run:")
        print("  python main_ui.py    # Launch the full UI")
        print("  python demo_ui.py    # Launch with demo data")
        return 0
    else:
        print("❌ Some tests failed. Check the errors above.")
        return 1


if __name__ == '__main__':
    sys.exit(main())
